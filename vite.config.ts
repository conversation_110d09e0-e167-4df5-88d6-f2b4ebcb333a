import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      server: {
        // Configure dev server for SPA routing
        historyApiFallback: {
          rewrites: [
            { from: /^\/en\/.*$/, to: '/index.html' },
            { from: /^\/cs\/.*$/, to: '/index.html' },
          ]
        }
      },
      preview: {
        // Configure preview server for SPA routing
        historyApiFallback: {
          rewrites: [
            { from: /^\/en\/.*$/, to: '/index.html' },
            { from: /^\/cs\/.*$/, to: '/index.html' },
          ]
        }
      },
      build: {
        outDir: 'dist',
        rollupOptions: {
          input: {
            main: path.resolve(__dirname, 'index.html'),
          }
        },
        // Ensure _redirects and _headers are copied
        copyPublicDir: true
      }
    };
});
