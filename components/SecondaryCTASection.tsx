import React from 'react';
import { COLORS, bookUrl } from '../constants';
import Button from './Button';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

const SecondaryCTASection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <SectionWrapper id="secondary-cta" bgClassName={`bg-[${COLORS.sageGreen}]`} contentClassName="text-center">
        <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.white}] mb-4`}>
          {t('secondaryCTA.title')}
        </h2>
        <p className={`text-lg text-[${COLORS.white}]/90 leading-relaxed mb-8 max-w-2xl mx-auto`}>
          {t('secondaryCTA.description')}
        </p>
        <Button href={`${bookUrl}`} variant="secondary" className={`bg-[${COLORS.white}] !text-[${COLORS.sageGreen}] hover:!bg-[${COLORS.offWhite}] hover:!text-[${COLORS.mutedTeal}]`}>
          {t('button.bookYourSessionNow')}
        </Button>
    </SectionWrapper>
  );
};

export default SecondaryCTASection;