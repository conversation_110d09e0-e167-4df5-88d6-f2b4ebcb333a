import React from 'react';
import { COLORS, EnvelopeIcon, PhoneIcon, FacebookIcon, InstagramIcon, YouTubeIcon } from '../constants';
// Button component is no longer needed in the simplified footer
// import Button from './Button'; 
import { useTranslation } from '../contexts/LanguageContext';

const Footer: React.FC = () => {
  const { t, language } = useTranslation();

  const scrollToSection = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    const targetId = href.substring(1); // Remove '#'
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  };
  return (
    <footer id="footer" className={`bg-[${COLORS.almostBlack}] text-[${COLORS.lightGray}]/70 py-12 md:py-16`}>
      <div className="container mx-auto max-w-7xl px-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10 md:gap-12 mb-10 md:mb-12">
          {/* Column 1: Contact */}
          <div>
            <h5 className={`font-headline text-xl font-semibold text-[${COLORS.white}] mb-4`}>{t('footer.contactBooking')}</h5>
            <ul className="space-y-3 text-base">
              <li>
                <a href="mailto:<EMAIL>" className="flex items-center hover:text-[${COLORS.sageGreen}] transition-colors">
                  <EnvelopeIcon className="w-5 h-5 mr-2 flex-shrink-0" /> <EMAIL>
                </a>
              </li>
              <li>
                <a href="https://wa.me/420607748413" className="flex items-center hover:text-[${COLORS.sageGreen}] transition-colors">
                  <PhoneIcon className="w-5 h-5 mr-2 flex-shrink-0" /> +420 607 748 413
                </a>
              </li>
            </ul>
          </div>

          {/* Column 2: Follow & Connect */}
          <div>
            <h5 className={`font-headline text-xl font-semibold text-[${COLORS.white}] mb-4`}>{t('footer.followConnect')}</h5>
            <div className="flex space-x-4 mb-3">
              <a href="https://www.facebook.com/martindoubravsky/" target="_blank" rel="noopener noreferrer" aria-label="Facebook" className={`hover:text-[${COLORS.sageGreen}] transition-colors`}>
                <FacebookIcon className="w-[22px] h-[22px]" />
              </a>
              <a href="https://www.instagram.com/martin_doubravsky/" target="_blank" rel="noopener noreferrer" aria-label="Instagram" className={`hover:text-[${COLORS.sageGreen}] transition-colors`}>
                <InstagramIcon className="w-[22px] h-[22px]" />
              </a>
              <a href="https://www.youtube.com/@zvedavaduse" target="_blank" rel="noopener noreferrer" aria-label="YouTube" className={`hover:text-[${COLORS.sageGreen}] transition-colors`}>
                <YouTubeIcon className="w-[22px] h-[22px]" />
              </a>
            </div>
            <a href="https://martindoubravsky.cz" target="_blank" rel="noopener noreferrer" className={`text-base hover:text-[${COLORS.sageGreen}] transition-colors`}>
              {t('footer.whatElseICreate')}
            </a>
          </div>
          
          {/* Column 3: Logo/Brand */}
           <div className="sm:col-span-2 lg:col-span-1 lg:justify-self-end lg:text-right">
             <a href="#hero" onClick={(e) => scrollToSection(e, '#hero')} className={`font-headline text-2xl font-bold text-[${COLORS.white}]`}>
                {t('site.name')}
             </a>
             <p className="text-sm mt-2">{t('site.subtitle')}</p>
           </div>

        </div>
        <div className={`border-t border-[${COLORS.lightGray}]/20 pt-8 text-center text-sm`}>
          <div className="mb-3">
            <a href={`/${language}/terms.html`} className={`hover:text-[${COLORS.sageGreen}] transition-colors mx-2`}>{t('footer.terms')}</a>
            <span className={`text-[${COLORS.lightGray}]/50`}>|</span>
            <a href={`/${language}/privacy.html`} className={`hover:text-[${COLORS.sageGreen}] transition-colors mx-2`}>{t('footer.privacy')}</a>
          </div>
          <p>{t('footer.copyright', { year: new Date().getFullYear().toString() })}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;