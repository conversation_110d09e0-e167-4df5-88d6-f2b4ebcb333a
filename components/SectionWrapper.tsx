
import React, { useRef, useEffect, useState } from 'react';

interface SectionWrapperProps {
  children: React.ReactNode;
  id?: string;
  className?: string;
  bgClassName?: string; // For background color of the section
  contentClassName?: string; // For styling the content container
  contentWidthClassName?: string; // Allows overriding the default max-width classes
}

const SectionWrapper: React.FC<SectionWrapperProps> = ({
  children,
  id,
  className = '',
  bgClassName = '',
  contentClassName = '',
  contentWidthClassName = 'max-w-6xl lg:max-w-7xl'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const domRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 }); // Trigger when 10% of the element is visible

    const { current } = domRef;
    if (current) {
      observer.observe(current);
    }

    return () => {
      if (current) {
        observer.unobserve(current);
      }
    };
  }, []);

  return (
    <section 
      id={id} 
      className={`${bgClassName} ${className} py-16 md:py-[100px] overflow-hidden`} // overflow-hidden for containing animations
      ref={domRef}
    >
      <div 
        className={`container mx-auto ${contentWidthClassName} px-6 
                    transition-all duration-700 ease-out
                    ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}
                    ${contentClassName}`}
      >
        {children}
      </div>
    </section>
  );
};

export default SectionWrapper;
