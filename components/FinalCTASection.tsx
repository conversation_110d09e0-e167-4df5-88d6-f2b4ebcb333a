import React from 'react';
import { COLORS, bookUrl } from '../constants';
import Button from './Button';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

const FinalCTASection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <SectionWrapper id="final-cta" bgClassName={`bg-[${COLORS.mutedTeal}]`} contentClassName="text-center">
        <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.white}] mb-4`}>
          {t('finalCTA.title')}
        </h2>
        <p className={`text-lg text-[${COLORS.white}]/90 mb-8 max-w-2xl mx-auto`}>
          {t('finalCTA.description')}
        </p>
        <Button href={`${bookUrl}`} variant="primary" className={`bg-[${COLORS.white}] !text-[${COLORS.mutedTeal}] hover:!bg-[${COLORS.offWhite}] hover:!text-[${COLORS.sageGreen}]`}>
          {t('button.bookYourSession')}
        </Button>
    </SectionWrapper>
  );
};

export default FinalCTASection;