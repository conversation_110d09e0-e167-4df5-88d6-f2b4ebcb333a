import React from 'react';
import { COLORS, bookUrl, StarIcon, CheckIcon } from '../constants';
import Button from './Button';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

interface PricingPackageData {
  nameKey: string;
  durationKey?: string;
  detailKeys: string[];
  priceKey: string; // Price string might also need to be a key if currency format changes
  priceDetail: string;
  isMostPopular?: boolean;
  ctaLink: string; // Links are usually language-agnostic
}

const pricingData: PricingPackageData[] = [
  {
    nameKey: "pricing.singleSession.name",
    durationKey: "pricing.singleSession.duration",
    detailKeys: [
      "pricing.singleSession.detail1",
      "pricing.singleSession.detail2",
      "pricing.singleSession.detail3",
      "pricing.singleSession.detail4"
    ],
    priceKey: "pricing.singleSession.price",
    priceDetail: "pricing.singleSession.priceDetail",
    ctaTextKey: "button.bookNow",
    ctaLink: "#booking-page-single"
  },
  {
    nameKey: "pricing.starterPackage.name",
    isMostPopular: true,
    durationKey: "pricing.starterPackage.duration",
    detailKeys: [
      "pricing.starterPackage.detail1",
      "pricing.starterPackage.detail2",
      "pricing.starterPackage.detail3"
    ],
    priceKey: "pricing.starterPackage.price",
    priceDetail: "pricing.starterPackage.priceDetail",
    ctaTextKey: "button.bookNow",
    ctaLink: "#booking-page-starter"
  },
  {
    nameKey: "pricing.ongoingCare.name",
    durationKey: "pricing.ongoingCare.duration",
    detailKeys: [
      "pricing.ongoingCare.detail1",
      "pricing.ongoingCare.detail2",
      "pricing.ongoingCare.detail3",
      "pricing.ongoingCare.detail4",
      "pricing.ongoingCare.detail5"
    ],
    priceKey: "pricing.ongoingCare.price",
    priceDetail: "pricing.ongoingCare.priceDetail",
    ctaTextKey: "button.bookNow",
    ctaLink: "#booking-page-ongoing"
  }
];

const PricingCard: React.FC<{ packageInfo: PricingPackageData }> = ({ packageInfo }) => {
  const { t } = useTranslation();
  const [isLoaded, setIsLoaded] = React.useState(false);
  React.useEffect(() => {
    if (packageInfo.isMostPopular) {
      const timer = setTimeout(() => setIsLoaded(true), 100); 
      return () => clearTimeout(timer);
    } else {
      setIsLoaded(true);
    }
  }, [packageInfo.isMostPopular]);

  return (
    <div className={`relative bg-[${COLORS.white}] p-8 rounded-lg shadow-xl flex flex-col h-full
                    ${packageInfo.isMostPopular ? `border-2 border-[${COLORS.sageGreen}] transform` : `border border-gray-200`}`}>
      {packageInfo.isMostPopular && (
        <div 
          className={`absolute -top-3 left-1/2 -translate-x-1/2 bg-[${COLORS.sageGreen}] text-[${COLORS.white}] text-xs font-semibold px-3 py-1 rounded-full flex items-center
                      transition-transform duration-250 ease-out ${isLoaded ? 'scale-100' : 'scale-0'}`}
        >
          <StarIcon className="w-3 h-3 mr-1" /> {t('pricing.mostPopularBadge')}
        </div>
      )}
      <h3 className={`font-body text-xl font-bold text-[${COLORS.almostBlack}] mb-2`}>{t(packageInfo.nameKey)}</h3>
      {packageInfo.durationKey && <p className={`text-sm text-[${COLORS.mediumGray}] leading-normal mb-4`}>{t(packageInfo.durationKey)}</p>}
      <div className="text-base text-[${COLORS.mediumGray}] leading-relaxed space-y-2 mt-4 mb-6 flex-grow">
        {packageInfo.detailKeys.map((detailKey, index) => (
          <div key={index} className="flex gap-1 items-top leading-normal text-balance"><CheckIcon className="w-5 h-5 flex-shrink-0" />{t(detailKey)}</div>
        ))}
      </div>
      
      <p className={`font-simple text-2xl font-bold text-[${COLORS.almostBlack}]`}>{t(packageInfo.priceKey)} <span className={`font-body text-sm font-light text-[${COLORS.mediumGray}]`}>{t(packageInfo.priceDetail)}</span></p>
    </div>
  );
};

const PricingSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <SectionWrapper id="pricing" bgClassName={`bg-[${COLORS.offWhite}]`}>
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-16`}>
        {t('pricing.title')}
      </h2>
      <div className="grid lg:grid-cols-3 gap-8 items-stretch mb-16">
        {pricingData.map((pkg, index) => (
          <PricingCard key={index} packageInfo={pkg} />
        ))}
      </div>
       <div className="flex flex-col justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6 mb-4">
          <Button href={`${bookUrl}`} variant="primary">{t('hero.ctaPrimary')}</Button>
        </div>
      <p className={`text-center text-sm text-[${COLORS.mediumGray}]/80 leading-normal`}>
        {t('pricing.footnote')}
      </p>
    </SectionWrapper>
  );
};

export default PricingSection;