import React from 'react';
import { COLORS } from '../constants';

const LoadingAnimation: React.FC = () => {
  return (
    <div 
      className="fixed inset-0 z-[100] flex flex-col items-center justify-center transition-opacity duration-300 ease-in-out"
      style={{ backgroundColor: COLORS.offWhite }} // Use offWhite from constants
      role="status"
      aria-live="polite"
      aria-label="Loading content"
    >
      <div className="app-loader-spinner" aria-hidden="true"></div>
    </div>
  );
};

export default LoadingAnimation;