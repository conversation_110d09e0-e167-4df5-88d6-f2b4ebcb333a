import React from 'react';
import { COLORS } from '../constants';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';
import OptimizedImage from './OptimizedImage';

const AboutBriefSection: React.FC = () => {
  const { t } = useTranslation();
  const headshotUrl = "/images/martin2-optimized.png";
  const headshotWebP = "/images/martin2.webp";
  const headshotLow = "/images/martin2-low.jpg";
  
  const scrollToMyStory = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const targetElement = document.getElementById('my-story');
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <SectionWrapper id="about-brief" bgClassName={`bg-[${COLORS.white}]`}>
      <div className="max-w-4xl mx-auto">
        <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
          {t('aboutBrief.title')}
        </h2>
        <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
          <OptimizedImage
            src={headshotUrl}
            webpSrc={headshotWebP}
            lowQualitySrc={headshotLow}
            alt={t('aboutBrief.headshotAlt')}
            className="w-[200px] h-[200px] rounded-full shadow-lg flex-shrink-0"
          />
          <div className="text-left md:text-left">
            <p className={`text-base text-[${COLORS.mediumGray}] leading-relaxed mb-4`}>
              {t('aboutBrief.bio')}
            </p>
            <a 
              href="#my-story" 
              onClick={scrollToMyStory}
              className={`font-body text-sm text-[${COLORS.sageGreen}] hover:text-[${COLORS.mutedTeal}] hover:underline uppercase tracking-[0.05em]`}
            >
              {t('aboutBrief.readFullStoryLink')} ↓
            </a>
          </div>
        </div>
      </div>
    </SectionWrapper>
  );
};

export default AboutBriefSection;