
import React from 'react';
import { PainPoint as PainPointType } from '../types';
import { COLORS, BoltIcon, FaceFrownIcon, BlockedEnergyIcon, HeartIcon as EmotionalHeartIcon, MentalClutterIcon } from '../constants';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

interface PainPointData {
  icon: React.ReactElement;
  titleKey: string;
  descriptionKey: string;
}

const painPointsData: PainPointData[] = [
  {
    icon: <MentalClutterIcon className={`w-10 h-10 text-[${COLORS.sageGreen}]`} />, // Re-using CpuChipIcon for focus
    titleKey: "painPoints.focusTitle",
    descriptionKey: "painPoints.focusDescription"
  },
  {
    icon: <FaceFrownIcon className={`w-10 h-10 text-[${COLORS.sageGreen}]`} />,
    titleKey: "painPoints.stressTitle",
    descriptionKey: "painPoints.stressDescription"
  },
  {
    icon: <BoltIcon className={`w-10 h-10 text-[${COLORS.sageGreen}]`} />,
    titleKey: "painPoints.fatigueTitle",
    descriptionKey: "painPoints.fatigueDescription"
  },
  {
    icon: <BlockedEnergyIcon className={`w-10 h-10 text-[${COLORS.sageGreen}]`} />,
    titleKey: "painPoints.painTitle",
    descriptionKey: "painPoints.painDescription"
  },
  // {
  //   icon: <EmotionalHeartIcon className={`w-10 h-10 text-[${COLORS.sageGreen}]`} />,
  //   titleKey: "painPoints.traumaTitle",
  //   descriptionKey: "painPoints.traumaDescription"
  // }
];

const PainPointCard: React.FC<{ painPoint: PainPointData }> = ({ painPoint }) => {
  const { t } = useTranslation();
  return (
    <div className={`bg-[${COLORS.white}] p-6 rounded-lg shadow-lg flex flex-col items-center text-center h-full`}>
      <div className="mb-4">{painPoint.icon}</div>
      <h3 className={`font-simple text-xl text-balance font-semibold text-[${COLORS.almostBlack}] mb-2`}>{t(painPoint.titleKey)}</h3>
      <p className={`text-base text-[${COLORS.mediumGray}] leading-relaxed text-pretty`}>{t(painPoint.descriptionKey)}</p>
    </div>
  );
};

const PainPointsSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <SectionWrapper id="pain-points" bgClassName={`bg-[${COLORS.offWhite}]`}>
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
        {t('painPoints.title')}
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
        {painPointsData.map((point, index) => (
          <PainPointCard key={`first-grid-${index}`} painPoint={point} />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default PainPointsSection;
