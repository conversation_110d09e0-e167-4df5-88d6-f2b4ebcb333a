import React from 'react';
import { COLORS, TensionKnotIcon, GrowthSproutIcon, DepletedBatteryIcon } from '../constants';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

interface ClientProfileData {
  icon: React.ReactElement;
  titleKey: string;
  descriptionKey: string;
}

const clientProfilesData: ClientProfileData[] = [
  {
    icon: <TensionKnotIcon className={`w-16 h-16 text-[${COLORS.sageGreen}]`} />,
    titleKey: "whoIsThisFor.professionalsTitle",
    descriptionKey: "whoIsThisFor.professionalsDescription"
  },
  {
    icon: <GrowthSproutIcon className={`w-16 h-16 text-[${COLORS.sageGreen}]`} />,
    titleKey: "whoIsThisFor.creativesTitle",
    descriptionKey: "whoIsThisFor.creativesDescription"
  },
  {
    icon: <DepletedBatteryIcon className={`w-16 h-16 text-[${COLORS.sageGreen}]`} />,
    titleKey: "whoIsThisFor.caregiversTitle",
    descriptionKey: "whoIsThisFor.caregiversDescription"
  }
];

const ClientProfileCard: React.FC<{ profile: ClientProfileData }> = ({ profile }) => {
  const { t } = useTranslation();
  return (
    <div className={`bg-[${COLORS.white}] p-8 rounded-lg shadow-lg flex flex-col items-center text-center h-full`}>
      <div className="mb-5">{profile.icon}</div>
      <div className="text-center">
        <h3 className={`font-simple text-2xl text-balance font-semibold text-[${COLORS.almostBlack}] mb-2`}>{t(profile.titleKey)}</h3>
        <p className={`text-base text-[${COLORS.mediumGray}] leading-relaxed text-pretty`}>{t(profile.descriptionKey)}</p>
      </div>
    </div>
  );
};

const WhoIsThisForSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <SectionWrapper id="who-is-this-for" bgClassName={`bg-[${COLORS.offWhite}]`}>
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
        {t('whoIsThisFor.title')}
      </h2>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {clientProfilesData.map((profile, index) => (
          <ClientProfileCard key={index} profile={profile} />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default WhoIsThisForSection;