
import React from 'react';
import { COLORS } from '../constants';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => void;
  href?: string;
  variant?: 'primary' | 'secondary' | 'text';
  className?: string;
  target?: string;
  rel?: string;
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  href, 
  variant = 'primary', 
  className = '',
  target,
  rel
}) => {
  const baseStyles = "font-body text-[18px] uppercase tracking-[0.05em] transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  let specificStyles = "";

  switch (variant) {
    case 'primary':
      specificStyles = `bg-[${COLORS.sageGreen}] text-[${COLORS.white}] hover:bg-[${COLORS.mutedTeal}] hover:-translate-y-0.5 px-8 py-3 rounded-md shadow-md focus:ring-[${COLORS.mutedTeal}]`;
      break;
    case 'secondary': // Not explicitly defined in prompt, but useful for variations
      specificStyles = `bg-[${COLORS.mutedTeal}] text-[${COLORS.white}] hover:bg-[${COLORS.sageGreen}] hover:-translate-y-0.5 px-8 py-3 rounded-md shadow-md focus:ring-[${COLORS.sageGreen}]`;
      break;
    case 'text':
      specificStyles = `text-[${COLORS.sageGreen}] hover:text-[${COLORS.mutedTeal}] hover:underline text-sm`;
      break;
  }

  if (href) {
    return (
      <a
        href={href}
        className={`${baseStyles} ${specificStyles} ${className} font-semibold`}
        onClick={onClick}
        target={target}
        rel={rel}
      >
        {children}
      </a>
    );
  }

  return (
    <button
      onClick={onClick}
      className={`${baseStyles} ${specificStyles} ${className}`}
    >
      {children}
    </button>
  );
};

export default Button;
