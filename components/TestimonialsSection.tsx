
import React from 'react';
import { COLORS, ChevronLeftIcon, ChevronRightIcon } from '../constants';
import <PERSON><PERSON>rapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

// Import Swiper React components
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, A11y } from 'swiper/modules';

// Interface for the structure of testimonial objects fetched from locale JSON
interface TestimonialFromLocale {
  photoUrl: string; 
  name: string;
  title?: string;
  quote: string;
}

const TestimonialCard: React.FC<{ testimonial: TestimonialFromLocale }> = ({ testimonial }) => {
  return (
    <div className={`bg-[${COLORS.white}] p-6 rounded-lg shadow-lg flex flex-col h-full`}>
      <p className={`font-quote text-base font-medium lg:text-lg text-balance text-[${COLORS.mediumGray}] leading-relaxed mb-4 flex-grow`}>“{testimonial.quote}”</p>
      <div>
        <h4 className={`font-body font-semibold text-[${COLORS.almostBlack}]`}>{testimonial.name}</h4>
        {/* {testimonial.title && <p className={`text-sm text-[${COLORS.sageGreen}]`}>{testimonial.title}</p>} */}
      </div>
    </div>
  );
};

const TestimonialsSection: React.FC = () => {
  const { t } = useTranslation();

  const testimonialsListUntyped = t('testimonials.list');
  let testimonialsList: TestimonialFromLocale[] = [];

  if (Array.isArray(testimonialsListUntyped)) {
    testimonialsList = testimonialsListUntyped as TestimonialFromLocale[];
  } else {
    console.warn("Testimonials list ('testimonials.list') not found or is not an array in the current locale file.");
  }

  const numTestimonials = testimonialsList.length;

  if (numTestimonials === 0) {
    return (
      <SectionWrapper id="testimonials" bgClassName={`bg-[${COLORS.sageGreen}]`}>
        <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.white}] text-center mb-12`}>
          {t('testimonials.title')}
        </h2>
        <p className="text-center text-white/80">{t('testimonials.noTestimonials') || 'No testimonials available at the moment.'}</p>
      </SectionWrapper>
    );
  }
  
  // Determine slidesPerView for desktop to set loop condition correctly
  const slidesPerViewDesktop = Math.min(3, numTestimonials);

  return (
    <SectionWrapper id="testimonials" bgClassName={`bg-[${COLORS.sageGreen}]`}>
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.white}] text-center mb-8`}>
        {t('testimonials.title')}
      </h2>
      <div className="relative">
        <Swiper
          modules={[Navigation, Pagination, A11y]}
          spaceBetween={30}
          slidesPerView={1} // Default for mobile
          loop={numTestimonials > slidesPerViewDesktop} // Loop if more items than shown on desktop
          navigation={{
            nextEl: '.swiper-button-next-custom-testimonials',
            prevEl: '.swiper-button-prev-custom-testimonials',
          }}
          pagination={{ clickable: true, el: '.swiper-pagination-custom-testimonials' }}
          breakpoints={{
            768: { // md breakpoint (Tailwind)
              slidesPerView: Math.min(2, numTestimonials),
              spaceBetween: 20,
            },
            1024: { // lg breakpoint (Tailwind)
              slidesPerView: slidesPerViewDesktop,
              spaceBetween: 30,
            },
          }}
          className="pb-12 md:pb-16" // Padding bottom for pagination
          // A11y props are handled by the A11y module from Swiper
        >
          {testimonialsList.map((testimonial, index) => (
            <SwiperSlide key={index} className="h-auto"> {/* h-auto so TestimonialCard's h-full works */}
              <TestimonialCard testimonial={testimonial} />
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Custom Navigation Buttons - Rendered if more than one testimonial, Swiper handles disabled state */}
        {numTestimonials > 1 && (
          <>
            <button
              aria-label={t('aria.prevTestimonial') || "Previous testimonial"}
              className="swiper-button-prev-custom-testimonials absolute top-1/2 -translate-y-1/2 left-0 z-20 p-2 bg-white/70 hover:bg-white rounded-full shadow-md transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-sageGreen md:-ml-4 lg:-ml-12"
            >
              <ChevronLeftIcon className="w-6 h-6 text-sageGreen" />
            </button>
            <button
              aria-label={t('aria.nextTestimonial') || "Next testimonial"}
              className="swiper-button-next-custom-testimonials absolute top-1/2 -translate-y-1/2 right-0 z-20 p-2 bg-white/70 hover:bg-white rounded-full shadow-md transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-sageGreen md:-mr-4 lg:-mr-12"
            >
              <ChevronRightIcon className="w-6 h-6 text-sageGreen" />
            </button>
          </>
        )}
        {/* Custom Pagination Container */}
        <div className="swiper-pagination-custom-testimonials text-center absolute bottom-0 left-0 right-0 h-10 flex justify-center items-center"></div>
      </div>
      <div className="max-w-3xl mx-auto text-center mt-0"> {/* Adjusted margin from mb-8 to mt-0 as pagination is now inside relative div */}
        <div className={`text-base text-[${COLORS.white}]/90 leading-relaxed max-w-[45rem] mx-auto`}>
          {t('testimonials.seeMoreReviews')}
          <a className={`text-[${COLORS.white}] underline hover:text-opacity-80`} href="https://www.google.com/search?client=safari&sca_esv=d6b7cd5f9271c9c7&rls=en&sxsrf=AE3TifP3_vfMbtwlttJy0t3LM5JaOSzjhQ%3A1749380869337&q=Kraniosakr%C3%A1ln%C3%AD%20terapie%20pro%20lidi%2C%20kte%C5%99%C3%AD%20cht%C4%9Bj%C3%AD%20od%20%C5%BEivota%20v%C3%ADc%20%7C%20Martin%20Doubravsk%C3%BD&stick=H4sIAAAAAAAAAONgU1I1qDAxN0hNTrIwMzQ0M7MwMze2MqhItUgytUw0SjZJMk-1MLI0X8Qa4V2UmJeZX5yYXXR4YU7e4bUKJalFiQWZqQoFRfkKOZkpmToK2SWpR2cCZZIzSo7MzgIy8lMUju7LLMsvSVQoO7w2WaFGwTexqCQzT8ElvzSpKLGsOPvwXgBNhxRghAAAAA&mat=CeNm1LwkCo_I&ved=2ahUKEwjHwqzK1-GNAxXTQkEAHX-KMe4QrMcEegQITRAC" target="_blank" rel="noopener noreferrer">
            {t('testimonials.seeMoreReviews2')}
          </a>{t('testimonials.seeMoreReviews3')}
        </div>
      </div>
    </SectionWrapper>
  );
};

export default TestimonialsSection;
