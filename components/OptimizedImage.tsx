import React, { useState, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  webpSrc?: string;
  lowQualitySrc?: string;
  priority?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  style,
  webpSrc,
  lowQualitySrc,
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || src);

  useEffect(() => {
    if (!priority) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      const element = document.getElementById(`img-${src.replace(/[^a-zA-Z0-9]/g, '')}`);
      if (element) {
        observer.observe(element);
      }

      return () => observer.disconnect();
    }
  }, [priority, src]);

  useEffect(() => {
    if (isInView && !isLoaded) {
      const img = new Image();
      
      // Try WebP first if supported and available
      const supportsWebP = () => {
        const canvas = document.createElement('canvas');
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      };

      const finalSrc = (webpSrc && supportsWebP()) ? webpSrc : src;
      
      img.onload = () => {
        setCurrentSrc(finalSrc);
        setIsLoaded(true);
      };
      
      img.onerror = () => {
        // Fallback to original if WebP fails
        if (finalSrc === webpSrc && webpSrc !== src) {
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            setCurrentSrc(src);
            setIsLoaded(true);
          };
          fallbackImg.src = src;
        } else {
          setIsLoaded(true);
        }
      };
      
      img.src = finalSrc;
    }
  }, [isInView, isLoaded, src, webpSrc]);

  return (
    <div
      id={`img-${src.replace(/[^a-zA-Z0-9]/g, '')}`}
      className={`relative overflow-hidden ${className}`}
      style={style}
    >
      <img
        src={currentSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-500 ${
          isLoaded ? 'opacity-100' : 'opacity-70'
        }`}
        style={{
          filter: isLoaded ? 'none' : 'blur(5px)',
          transform: isLoaded ? 'scale(1)' : 'scale(1.05)',
          transition: 'all 0.5s ease-out'
        }}
      />
      
      {!isLoaded && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse" />
      )}
    </div>
  );
};

export default OptimizedImage;
