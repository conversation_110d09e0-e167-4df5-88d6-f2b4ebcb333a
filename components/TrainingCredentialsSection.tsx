import React from 'react';
import { COLORS, ShieldCheckIcon, StudyIcon, VerifiedCredentialsIcon, PeacefulSunriseIcon } from '../constants';
import SectionWrapper from './SectionWrapper';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from '../contexts/LanguageContext';

interface CredentialData {
  icon: React.ReactElement;
  studyPoint: string;
}

const CredentialsData: CredentialData[] = [
  {
    icon: <StudyIcon className={`w-8 h-8 text-[${COLORS.sageGreen}]`} />,
    studyPoint: "trainingCredentials.studyPoint1",
  },
  {
    icon: <ShieldCheckIcon className={`w-8 h-8 text-[${COLORS.sageGreen}]`} />,
    studyPoint: "trainingCredentials.studyPoint2",
  },
  {
    icon: <PeacefulSunriseIcon className={`w-8 h-8 text-[${COLORS.sageGreen}]`} />,
    studyPoint: "trainingCredentials.studyPoint3",
  },
  {
    icon: <VerifiedCredentialsIcon className={`w-8 h-8 text-[${COLORS.sageGreen}]`} />,
    studyPoint: "trainingCredentials.studyPoint4",
  }
];
const CredentialsCard: React.FC<{ profile: CredentialData }> = ({ profile }) => {
  const { t } = useTranslation();
  return (
    <div className={`flex items-start space-x-4 p-6 rounded-lg shadow-sm bg-[${COLORS.mutedTeal}]/10`}>
      <div className="flex-shrink-0 mt-1">
        {profile.icon}
      </div>
      <div className="text-lg">
       <ReactMarkdown>{t(profile.studyPoint)}</ReactMarkdown>
      </div>
    </div>
  );
};

const CredentialsSection: React.FC = () => {
  const { t } = useTranslation();

  return (
    <SectionWrapper id="training-credentials" bgClassName={`bg-[${COLORS.white}]`}>
      <div className="max-w-3xl mx-auto text-center">
        <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] mb-12`}>
          {t('trainingCredentials.title')}
        </h2>
        <div className={`text-base text-[${COLORS.mediumGray}] leading-relaxed space-y-4 text-left`}>
          <div class="max-w-4xl mx-auto space-y-8">
             {CredentialsData.map((profile, index) => (
              <CredentialsCard key={index} profile={profile} />
            ))}
          </div>
        </div>
      </div>
    </SectionWrapper>
  );
};

export default CredentialsSection;