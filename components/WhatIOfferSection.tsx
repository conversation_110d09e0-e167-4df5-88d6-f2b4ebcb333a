import React from 'react';
import { COLORS, HandRaisedIcon, CpuChipIcon, ShieldCheckIcon, CheckIcon } from '../constants';
import SectionWrapper from './SectionWrapper';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from '../contexts/LanguageContext';

interface WhyItWorksItem {
  icon: React.ReactElement;
  titleKey: string;
  captionKey: string;
}

const whyItWorksData: WhyItWorksItem[] = [
  {
    icon: <CpuChipIcon className={`w-10 h-10 text-[${COLORS.sageGreen}] mb-2`} />,
    titleKey: "whatIOffer.nervousSystemResetTitle",
    captionKey: "whatIOffer.nervousSystemResetCaption"
  },
  {
    icon: <HandRaisedIcon className={`w-10 h-10 text-[${COLORS.sageGreen}] mb-2`} />,
    titleKey: "whatIOffer.gentleTouchTitle",
    captionKey: "whatIOffer.gentleTouchCaption"
  },
  {
    icon: <ShieldCheckIcon className={`w-10 h-10 text-[${COLORS.sageGreen}] mb-2`} />,
    titleKey: "whatIOffer.traumaInformedApproachTitle",
    captionKey: "whatIOffer.traumaInformedApproachCaption"
  }
];

const WhatIOfferSection: React.FC = () => {
  const { t } = useTranslation();
  const benefitsKeys = [
    "whatIOffer.benefit1",
    "whatIOffer.benefit2",
    "whatIOffer.benefit3",
    "whatIOffer.benefit4",
    "whatIOffer.benefit5",
  ];

  return (
    <SectionWrapper id="what-i-offer" bgClassName={`bg-[${COLORS.white}]`}>
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-8`}>
        {t('whatIOffer.title')}
      </h2>
      <div className="max-w-3xl mx-auto text-center">
        <div className={`text-xl text-[${COLORS.mediumGray}] leading-relaxed mb-8`}>
          <ReactMarkdown>{t('whatIOffer.description')}</ReactMarkdown>
        </div>
      </div>
      <div className="max-w-2xl mx-auto text-left">
        <div className={`text-base text-[${COLORS.mediumGray}] leading-relaxed space-y-2 mb-12`}>
          {benefitsKeys.map(key => <div className="flex items-start space-x-2"><svg className={`w-6 h-6 flex-shrink-0`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#A1C99A"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"></path></svg><p className="text-lg text-pretty" key={key}>{t(key)}</p></div>)}
        </div>
        <div className={`text-xl text-[${COLORS.mediumGray}] leading-relaxed text-center`}>
          <ReactMarkdown>{t('whatIOffer.highlight')}</ReactMarkdown>
        </div>
        {/* <div className="space-y-8">
          {whyItWorksData.map((item, index) => (
            <div key={index} className="flex items-start space-x-4">
              <div className="flex-shrink-0">{item.icon}</div>
              <div>
                <h3 className={`font-body text-xl font-semibold text-[${COLORS.almostBlack}] mb-1`}>{t(item.titleKey)}</h3>
                <p className={`text-base text-[${COLORS.mediumGray}] leading-relaxed`}>{t(item.captionKey)}</p>
              </div>
            </div>
          ))}
        </div> */}
      </div>
    </SectionWrapper>
  );
};

export default WhatIOfferSection;