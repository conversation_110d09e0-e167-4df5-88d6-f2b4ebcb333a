import React, { useState } from 'react';
import { COLORS, ChevronDownIcon } from '../constants';
import { useTranslation } from '../contexts/LanguageContext'; // Though t might not be directly used if q/a are pre-translated

interface AccordionItemProps {
  question: string; // This will be the translated question string
  answer: string;   // This will be the translated answer string
  idBase: string;   // For unique aria-controls
}

const AccordionItem: React.FC<AccordionItemProps> = ({ question, answer, idBase }) => {
  const [isOpen, setIsOpen] = useState(false);
  // const { t } = useTranslation(); // Not strictly needed if q/a are already translated strings

  return (
    <div className={`border-b border-[${COLORS.lightGray}]`}>
      <h3>
        <button
          type="button"
          className="flex justify-between items-center w-full py-5 text-left"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
          aria-controls={`faq-answer-${idBase}`}
        >
          <span className={`font-body text-lg font-semibold text-[${COLORS.almostBlack}]`}>{question}</span>
          <ChevronDownIcon 
            className={`w-5 h-5 text-[${COLORS.sageGreen}] transform transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`} 
          />
        </button>
      </h3>
      <div
        id={`faq-answer-${idBase}`}
        className={`overflow-hidden transition-all duration-300 ease-in-out ${isOpen ? 'max-h-screen opacity-100 pb-5' : 'max-h-0 opacity-0'}`}
      >
        <p className={`text-base text-[${COLORS.mediumGray}] leading-relaxed`}>{answer}</p>
      </div>
    </div>
  );
};

export default AccordionItem;