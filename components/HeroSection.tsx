import React from 'react';
import { COLORS, bookUrl } from '../constants';
import Button from './Button';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';
import ProgressiveBackgroundImage from './ProgressiveBackgroundImage';

const HeroSection: React.FC = () => {
  const { t } = useTranslation();
  const heroImageUrl = "/images/hero-optimized.png"; // Optimized PNG fallback
  const heroImageLowQuality = "/images/hero-low.jpg"; // Tiny placeholder (565B)
  const heroImageWebP = "/images/hero.webp"; // WebP version (146KB - 94% smaller!)

  const scrollToWhatIOffer = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    const targetElement = document.getElementById('pain-points');
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <SectionWrapper id="hero" bgClassName={`bg-cover bg-center bg-no-repeat flex items-center`} className="!py-0" contentClassName="!max-w-full !px-0">
      <ProgressiveBackgroundImage
        src={heroImageUrl}
        lowQualitySrc={heroImageLowQuality}
        webpSrc={heroImageWebP}
        priority={true}
        className="w-full flex items-center justify-center text-center px-6 pt-10 md:pt-24 2xl:pt-32 pb-12 md:pb-24"
      >
        <div className={`absolute inset-0 bg-black/40`}></div> {/* Semi-transparent overlay */}
        <div className="relative z-10 max-w-3xl xl:max-w-4xl 2xl:max-w-6xl mx-auto">
          <h1 id="hero-headline" className={`font-headline text-3xl md:text-4xl lg:text-5xl 2xl:text-6xl leading-tight lg:leading-tight 2xl:leading-tight font-bold text-[${COLORS.white}] mb-6 text-pretty`}>
            {t('hero.headline')}
          </h1>
          <h2 className={`max-w-3xl mx-auto text-pretty font-body text-lg md:text-xl lg:text-2xl leading-normal lg:leading-normal text-[${COLORS.white}]/90 mb-10 text-pretty`}>
            {t('hero.subheadline')}
          </h2>
          <div className={`relative bg-white/20 bg-gradient-to-br from-[#805b1c]/20 to-[#6c4d1736]/20 backdrop-blur-md p-6 rounded-xl max-w-lg mx-auto shadow-lg border border-white/20 mb-10`}>
            <span 
              aria-hidden="true" 
              className={`absolute -top-4 -left-3 font-merriweather text-8xl text-[${COLORS.sageGreen}] opacity-30 select-none`}
            >
              “
            </span>
            <blockquote className="relative z-10">
              <p className={`font-quote text-base lg:text-lg leading-relaxed text-[${COLORS.white}] mb-3 text-pretty text-left`}>
                {t('hero.testimonialQuote')}
              </p>
              <footer className={`text-right`}>
                <span className={`font-body text-base sm:text-lg text-[${COLORS.white}]`}>{t('hero.testimonialAttributionName')}</span>
              </footer>
            </blockquote>
          </div>
          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-6 mb-10">
            <Button href={`${bookUrl}`} variant="primary">{t('hero.ctaPrimary')}</Button>
            <a 
              href="#pain-points" 
              onClick={scrollToWhatIOffer}
              className={`font-body text-sm text-[${COLORS.white}]/80 hover:text-[${COLORS.white}] hover:underline uppercase tracking-[0.05em]`}
            >
              {t('hero.ctaSecondary')}
            </a>
          </div>
        </div>
      </ProgressiveBackgroundImage>
    </SectionWrapper>
  );
};

export default HeroSection;