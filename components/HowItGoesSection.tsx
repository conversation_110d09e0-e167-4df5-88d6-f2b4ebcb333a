
import React, { useState, useEffect, useRef } from 'react';
import { COLORS } from '../constants';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

interface HowitgoesFromLocale {
  no: string;
  title: string;
  text: string;
}

const HowitgoesCard: React.FC<{ step: HowitgoesFromLocale }> = ({ step }) => {
  // Direct props are already translated
  return (

    // <div class="bg-white p-6 rounded-xl shadow-lg">
    //   <div class="flex items-center mb-4">
    //     <span class="bg-brand-secondary text-white rounded-full w-10 h-10 flex items-center justify-center text-xl font-bold mr-4">1</span>
    //     <h3 class="text-xl font-semibold text-brand-primary">Úvodn<PERSON> rozhovor</h3>
    //     </div>
    //     <p class="text-brand-dark text-opacity-80"><PERSON><PERSON><PERSON><PERSON><PERSON> si povídáme o va<PERSON>ich potřeb<PERSON>, záměrech a očekáváních. Uvolníte se, dáme si čaj.</p></div>

    
    <div className={`bg-[${COLORS.white}] p-6 rounded-lg shadow-lg flex flex-col h-full min-w-[300px] md:min-w-0 snap-start flex-shrink-0`}>
      <div className="flex items-center mb-4">
        <span className={`bg-[${COLORS.sageGreen}] text-white rounded-full w-10 h-10 flex items-center justify-center text-xl font-bold mr-4`}>{step.no}</span>
        <h3 className={`font-body text-xl font-semibold text-[${COLORS.almostBlack}]`}>{step.title}</h3>
      </div>
      <p className="text-pretty">{step.text}</p>
    </div>
  );
};

const HowItGoesSection: React.FC = () => {
  const { t } = useTranslation();
  const howitgoesListUntyped = t('howitgoes.list');
  let howitgoesList: HowitgoesFromLocale[] = [];

  if (Array.isArray(howitgoesListUntyped)) {
    howitgoesList = howitgoesListUntyped as HowitgoesFromLocale[];
  } else {
    console.warn("HowItGoes list ('howitgoes.list') not found or is not an array in the current locale file.");
  }

  return (
    <SectionWrapper id="how-it-goes" bgClassName={`bg-[${COLORS.offWhite}]`}>
      <h2 className={`font-body text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
        {t('howitgoes.title')}
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
        {howitgoesList.map((step, index) => (
          <HowitgoesCard key={index} step={step} />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default HowItGoesSection;
