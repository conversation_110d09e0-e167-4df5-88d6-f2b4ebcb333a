import React from 'react';
import { COLORS } from '../constants';
import AccordionItem from './AccordionItem';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';

interface FAQItemData {
  questionKey: string;
  answerKey: string;
  idBase: string;
}

// Generate FAQ keys dynamically
const totalFAQs = 8;
const faqData: FAQItemData[] = Array.from({ length: totalFAQs }, (_, index) => {
  const idBase = `q${index + 1}`;
  return {
    idBase,
    questionKey: `faq.items.${idBase}.question`,
    answerKey: `faq.items.${idBase}.answer`,
  };
});

const FAQSection: React.FC = () => {
  const { t } = useTranslation();



  return (
    <SectionWrapper id="faq" bgClassName={`bg-[${COLORS.offWhite}]`}>
      <div className="max-w-3xl mx-auto">
        <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
          {t('faq.title')}
        </h2>
        <div className="space-y-0">
          {faqData.map((item) => (
            <AccordionItem 
              key={item.idBase} 
              question={t(item.questionKey)} 
              answer={t(item.answerKey)}
              idBase={item.idBase}
            />
          ))}
        </div>
      </div>
    </SectionWrapper>
  );
};

export default FAQSection;