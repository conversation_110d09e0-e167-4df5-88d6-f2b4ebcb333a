import React, { useState, useEffect } from 'react';
import { NAV_ITEMS, COLORS, bookUrl, Bars3Icon, XMarkIcon } from '../constants';
import Button from './Button';
import { useTranslation } from '../contexts/LanguageContext';

const Header: React.FC = () => {
  const { t, language, setLanguage } = useTranslation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    const targetId = href.substring(1); // Remove '#'
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  };

  const handleLanguageChange = (lang: string) => {
    setLanguage(lang);
    if (isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
    }
  }

  return (
    <header 
      className={`top-0 left-0 right-0 z-50 transition-all duration-300 
                  ${isScrolled || isMobileMenuOpen ? `bg-[${COLORS.white}] shadow-lg` : 'bg-white/85'}`}
    >
      <div className="container mx-auto max-w-7xl px-4 sm:px-6">
        <div className="flex items-center justify-between h-24 sm:h-32">
          <a href="#hero" onClick={(e) => scrollToSection(e, '#hero')} className={`flex items-center text-[${COLORS.almostBlack}]`}>
            <img src="/images/craniocare-logo.svg" alt="CranioCare" className="h-10 mr-2 inline-block hidden min-[400px]:inline-block" />
            <div>
            <span className='font-headline text-sm xs:text-base sm:text-lg xl:text-xl font-bold leading-tight'>{t('site.name')}</span>
            <span className="block font-cta text-xs sm:text-sm xl:text-base font-normal tracking-normal"><span>{t('site.subtitle')}</span>
            {/* TURN ON WHEN BCST!!!! , <abbr title={t('site.abbr')}>{t('site.bcst')}</abbr> */}
            </span>
            </div>
          </a>
          {/* Desktop Navigation */}
          <nav className="hidden lg:flex space-x-4 items-center font-simple">
            {NAV_ITEMS.map((item) => (
              <a
                key={item.translationKey}
                href={item.href}
                onClick={(e) => scrollToSection(e, item.href)}
                className={`text-sm xl:text-base text-nowrap text-[${COLORS.mediumGray}] hover:text-[${COLORS.sageGreen}] transition-colors px-2`}
              >
                {t(item.translationKey)}
              </a>
            ))}
            
            {/* Language Switcher - Desktop */}
            <div className="flex items-center space-x-1 pl-3 ml-1">
              <button
                onClick={() => handleLanguageChange('en')}
                className={`text-sm font-medium px-1 py-1 rounded 
                            ${language === 'en' ? `text-[${COLORS.sageGreen}] font-bold` : `text-[${COLORS.mediumGray}] hover:text-[${COLORS.almostBlack}]`}`}
                aria-label="Switch to English"
              >
                EN
              </button>
              <span className={`text-sm text-[${COLORS.lightGray}]`}>|</span>
              <button
                onClick={() => handleLanguageChange('cs')}
                className={`text-sm font-medium px-1 py-1 rounded 
                            ${language === 'cs' ? `text-[${COLORS.sageGreen}] font-bold` : `text-[${COLORS.mediumGray}] hover:text-[${COLORS.almostBlack}]`}`}
                aria-label="Přepnout na Češtinu"
              >
                CZ
              </button>
            </div>
            <Button href={`${bookUrl}`} variant="primary" className="text-sm !px-4 !py-2 ml-2">{t('button.bookNow')}</Button>
          </nav>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label={t('aria.toggleMenu')}
              className={`text-[${COLORS.almostBlack}]`}
            >
              {isMobileMenuOpen ? <XMarkIcon className="w-7 h-7" /> : <Bars3Icon className="w-7 h-7" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className={`md:hidden fixed inset-0 top-20 bg-[${COLORS.white}] z-40 flex flex-col items-center justify-center p-6`}>
          <nav className="flex flex-col items-center space-y-6">
            {NAV_ITEMS.map((item) => (
              <a
                key={item.translationKey}
                href={item.href}
                onClick={(e) => scrollToSection(e, item.href)}
                className={`text-2xl text-[${COLORS.almostBlack}] hover:text-[${COLORS.sageGreen}] transition-colors`}
              >
                {t(item.translationKey)}
              </a>
            ))}
          </nav>
          {/* Language Switcher - Mobile */}
          <div className="flex items-center space-x-2 mt-8">
            <button
              onClick={() => handleLanguageChange('en')}
              className={`text-xl px-2 py-1 rounded 
                          ${language === 'en' ? `text-[${COLORS.sageGreen}] font-bold` : `text-[${COLORS.almostBlack}] hover:text-[${COLORS.sageGreen}]`}`}
              aria-label="Switch to English"
            >
              EN
            </button>
            <span className={`text-xl text-[${COLORS.almostBlack}]`}>|</span>
            <button
              onClick={() => handleLanguageChange('cs')}
              className={`text-xl px-2 py-1 rounded 
                          ${language === 'cs' ? `text-[${COLORS.sageGreen}] font-bold` : `text-[${COLORS.almostBlack}] hover:text-[${COLORS.sageGreen}]`}`}
              aria-label="Přepnout na Češtinu"
            >
              CZ
            </button>
          </div>
          <Button href={`${bookUrl}`} variant="primary" className="mt-8 w-full max-w-xs text-center">{t('button.bookNow')}</Button>
        </div>
      )}
    </header>
  );
};

export default Header;