import React from 'react';
import { COLORS } from '../constants';
import SectionWrapper from './SectionWrapper';
import { useTranslation } from '../contexts/LanguageContext';
import OptimizedImage from './OptimizedImage';

interface LocationData {
  nameKey: string;
  addressKey: string;
  detailsKey: string;
  mapImageUrl: string; // Optimized image
  mapImageWebP: string; // WebP version
  mapImageLow: string; // Low quality placeholder
  mapIframe: string;
  mapLink: string; // Map links might be language-specific if Google Maps supports it well through URL
}

const locationsData: LocationData[] = [
  {
    nameKey: "locations.jablonec.name",
    addressKey: "locations.jablonec.address",
    detailsKey: "locations.jablonec.details",
    mapImageUrl: "/images/craniocare-jablonec-optimized.jpg",
    mapImageWebP: "/images/craniocare-jablonec.webp",
    mapImageLow: "/images/craniocare-jablonec-low.jpg",
    mapIframe: '<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d583.0684794859168!2d15.17121765132084!3d50.7237569441937!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x470ecad039c2f7b7%3A0x171cd5c3a7771d48!2sPodhorsk%C3%A1%20670%2F8%2C%20466%2001%20Jablonec%20nad%20Nisou%201!5e0!3m2!1sen!2scz!4v1751449128153!5m2!1sen!2scz" width="400" height="250" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>',
    mapLink: "https://maps.app.goo.gl/B82WYQVhXawUJrWF8" // Consider lang param for map link if needed
  },
  // {
  //   nameKey: "locations.prague.name",
  //   addressKey: "locations.prague.address",
  //   detailsKey: "locations.prague.details",
  //   mapImageUrl: "/images/craniocare-praha-optimized.jpg",
  //   mapImageWebP: "/images/craniocare-praha.webp",
  //   mapImageLow: "/images/craniocare-praha-low.jpg",
  //   mapLink: "https://maps.google.com/?q=Studio+Loona,Křižíkova+67,Prague"
  // }
];

const LocationCard: React.FC<{ location: LocationData }> = ({ location }) => {
  const { t } = useTranslation();
  return (
    <div className={`bg-[${COLORS.white}] p-6 max-w-3xl mx-auto`}>
      <a className="relative top-0 hover:-top-1 duration-300 ease-out block" href={location.mapLink} target="_blank" rel="noopener noreferrer" aria-label={`${t('aria.mapOf')} ${t(location.nameKey)}`}>
        <OptimizedImage
          src={location.mapImageUrl}
          webpSrc={location.mapImageWebP}
          lowQualitySrc={location.mapImageLow}
          alt={`${t('aria.mapOf')} ${t(location.nameKey)}`}
          className="rounded-md mb-4 border border-gray-200"
        />
      </a>
      {/* <div dangerouslySetInnerHTML={{ __html: location.mapIframe }} className='mb-4 max-w-[90vw] truncate'/> */}
      <h3 className={`font-body text-xl font-semibold text-[${COLORS.almostBlack}]`}>{t(location.nameKey)}</h3>
      <p className={`text-lg text-[${COLORS.mediumGray}] leading-relaxed mb-1`}>
        <a className="hover:underline" href={location.mapLink} target='_blank'>{t(location.addressKey)}</a>
      </p>
      {/* <p className={`text-sm text-[${COLORS.mutedTeal}] mb-3`}>{t(location.detailsKey)}</p> */}
    </div>
  );
};

const LocationsSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <SectionWrapper id="locations" bgClassName={`bg-[${COLORS.white}]`}>
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
        {t('locations.title')}
      </h2>
      <div className="flex justify-center max-w-4xl mx-auto">
        {locationsData.map((location, index) => (
          <LocationCard key={index} location={location} />
        ))}
      </div>
    </SectionWrapper>
  );
};

export default LocationsSection;