import React, { useRef, useEffect, useCallback } from 'react';
import { COLORS } from '../constants';
import SectionWrapper from './SectionWrapper';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from '../contexts/LanguageContext';
import Markdown from 'react-markdown';
import OptimizedImage from './OptimizedImage';

const MyStorySection: React.FC = () => {
  const { t } = useTranslation();
  const candidPhotoUrl = "/images/martin-optimized.jpg";
  const candidPhotoWebP = "/images/martin.webp";
  const candidPhotoLow = "/images/martin-low.jpg";
  const storyParagraphKeys = [
    "myStory.paragraph1",
    "myStory.paragraph2",
    "myStory.paragraph3",
    "myStory.paragraph4",
    "myStory.paragraph5",
    "myStory.paragraph6"
  ];

  const imgRef = useRef<HTMLImageElement>(null);
  const animationFrameId = useRef<number | null>(null);

  const handleScroll = useCallback(() => {
    if (!imgRef.current) return;

    const image = imgRef.current;
    const { top, height } = image.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    // Calculate the middle of the image relative to the viewport top
    const imageMiddleInViewport = top + height / 2;
    
    // Calculate the difference from the viewport center
    // A positive diff means image middle is below viewport middle
    // A negative diff means image middle is above viewport middle
    const diff = imageMiddleInViewport - viewportHeight / 2 - 100;

    // Parallax factor - smaller for more subtle effect
    const parallaxFactor = 0.05; // Adjust for desired intensity
    let translateY = -diff * parallaxFactor;

    // Clamp the translation to avoid excessive movement
    const maxOffset = 30; // Max pixels the image can move up or down
    translateY = Math.max(-maxOffset, Math.min(maxOffset, translateY));
    
    image.style.transform = `translateY(${translateY}px)`;
  }, []);

  useEffect(() => {
    const onScroll = () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
      animationFrameId.current = requestAnimationFrame(handleScroll);
    };

    // Initial call to set position if image is already in view
    if (imgRef.current) {
        // Delay initial call slightly to ensure layout is stable
        setTimeout(() => requestAnimationFrame(handleScroll), 100);
    }

    window.addEventListener('scroll', onScroll, { passive: true });
    window.addEventListener('resize', onScroll, { passive: true }); // Also adjust on resize

    return () => {
      window.removeEventListener('scroll', onScroll);
      window.removeEventListener('resize', onScroll);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
      // Reset transform on unmount if needed, though usually not necessary if element is removed
      if (imgRef.current) {
        imgRef.current.style.transform = 'translateY(0px)';
      }
    };
  }, [handleScroll]);

  return (
    <SectionWrapper id="my-story" bgClassName={`bg-[${COLORS.white}]`} contentWidthClassName="max-w-5xl lg:max-w-6xl">
      <h2 className={`font-headline text-3xl md:text-4xl font-bold text-[${COLORS.almostBlack}] text-center mb-12`}>
        {t('myStory.title')}
      </h2>
      <div className="grid lg:grid-cols-7 gap-12 items-start">
        <div className={`text-base text-[${COLORS.almostBlack}] leading-relaxed space-y-4 max-w-prose text-pretty col-span-3`}>
          {storyParagraphKeys.map(key => <Markdown key={key}>{t(key)}</Markdown>)}
          <h4 className={`font-headline text-xl font-bold text-[${COLORS.almostBlack}]`}>- Martin Doubravský</h4>
        </div>
        <div className="flex justify-center lg:justify-start col-span-4">
          {/* The image container can have overflow-hidden if the image itself is scaled larger than its visual box for parallax */}
          {/* For simple translateY, direct parent doesn't strictly need overflow-hidden unless image itself is larger than desired viewport for it */}
          <div ref={imgRef} className="grow transition-transform duration-100 ease-out" style={{ willChange: 'transform' }}>
            <OptimizedImage
              src={candidPhotoUrl}
              webpSrc={candidPhotoWebP}
              lowQualitySrc={candidPhotoLow}
              alt={t('myStory.candidPhotoAlt')}
              className="rounded-lg shadow-xl max-h-[600px] w-auto border border-gray-200"
            />
          </div>
        </div>
      </div>
    </SectionWrapper>
  );
};

export default MyStorySection;