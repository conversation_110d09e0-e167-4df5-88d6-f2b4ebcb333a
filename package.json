{"name": "craniosacral-practice-by-martin-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run optimize-images && vite build", "build:ci": "vite build", "build:fast": "vite build", "preview": "vite preview", "deploy": "npm run build && npx wrangler deploy", "cf:dev": "npm run build && npx wrangler dev", "optimize-images": "./scripts/optimize-images.sh", "optimize-image": "./scripts/optimize-images.sh"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@cloudflare/kv-asset-handler": "^0.4.0", "@types/node": "^22.14.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "lucide": "^0.513.0", "lucide-react": "^0.513.0", "react-markdown": "^10.1.0", "swiper": "^11.2.8", "typescript": "~5.7.2", "vite": "^6.2.0", "wrangler": "^4.19.1"}}