<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <base href="/">
    <title>Craniosacral Therapy Practice | <PERSON></title>
    <meta name="description" content="Professional craniosacral therapy services. Gentle care for stress relief, pain management, and improved well-being.">
    <meta name="keywords" content="craniosacral therapy, stress relief, pain management, holistic healing, wellness, biodynamic">
    
    <!-- Fallback Open Graph Tags -->
    <meta property="og:title" content="Craniosacral Therapy Practice">
    <meta property="og:description" content="Professional craniosacral therapy services. Gentle care for stress relief, pain management, and improved well-being.">
    <meta property="og:site_name" content="Craniosacral Therapy Practice">
    <meta property="og:type" content="website">
    <meta property="og:image" content="/og-image.png?v=2"> 
    
    <!-- Fallback Twitter Card Tag -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Craniosacral Therapy Practice">
    <meta name="twitter:description" content="Professional craniosacral therapy services. Gentle care for stress relief, pain management, and improved well-being.">
    <meta name="twitter:image" content="/og-image.png?v=2">

    <!-- Favicon Links -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Eczar:ital,wght@0,300..700;1,300..700&family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiJ-Ek-_EeA.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://fonts.gstatic.com/s/inter/v18/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyYAZ9hiJ-Ek-_EeA.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Preload critical images -->
    <link rel="preload" href="/images/hero-low.jpg" as="image">
    <link rel="preload" href="/images/hero.webp" as="image" type="image/webp">
    <link rel="preload" href="/images/hero-optimized.png" as="image">

    <!-- Preload other critical images -->
    <link rel="preload" href="/images/martin2-low.jpg" as="image">
    <link rel="preload" href="/images/martin2.webp" as="image" type="image/webp">
    <!-- Swiper.js CSS -->
    <link rel="stylesheet" href="https://esm.sh/swiper@11/swiper.min.css" />
    <link rel="stylesheet" href="https://esm.sh/swiper@11/modules/navigation.min.css" />
    <link rel="stylesheet" href="https://esm.sh/swiper@11/modules/pagination.min.css" />

    <style>
      /* Smooth scroll for anchor links */
      html {
        scroll-behavior: smooth;
      }
      .font-body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-optical-sizing: auto;
        font-style: normal;
        font-display: swap;
      }

      .font-headline {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-optical-sizing: auto;
        font-style: normal;
        font-display: swap;
      }

      .font-quote {
        font-family: "IBM Plex Sans", Georgia, "Times New Roman", Times, serif;
        font-optical-sizing: auto;
        font-style: italic;
        font-variation-settings: "wdth" 100;
        font-display: swap;
      }

      /* Custom Swiper Pagination Styles */
      .swiper-pagination-custom-testimonials .swiper-pagination-bullet {
        background-color: #E1E1DD; /* lightGray from constants.tsx */
        opacity: 0.7;
        width: 10px;
        height: 10px;
        margin: 0 5px !important;
        transition: background-color 0.3s, opacity 0.3s;
      }
      .swiper-pagination-custom-testimonials .swiper-pagination-bullet-active {
        background-color: #A1C99A; /* sageGreen from constants.tsx */
        opacity: 1;
      }

      /* Custom Swiper Navigation Button Disabled States */
      .swiper-button-prev-custom-testimonials.swiper-button-disabled,
      .swiper-button-next-custom-testimonials.swiper-button-disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      /* Loading Spinner Styles */
      .app-loader-spinner {
        border: 5px solid rgba(161, 201, 154, 0.2); /* sageGreen with low opacity for track */
        width: 48px;
        height: 48px;
        border-radius: 50%;
        border-left-color: #A1C99A; /* sageGreen from constants.tsx for the active part */
        animation: app-loader-spin 1s linear infinite;
      }
      @keyframes app-loader-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      #my-story a {
        text-decoration: underline;
        color: #7a9a74; /* sageGreen from constants.tsx */
        &:hover {
          color: #7FB7A4; /* mutedTeal from constants.tsx */
        }
      }

      /* Progressive image loading styles */
      .progressive-image {
        transition: filter 0.3s ease, transform 0.3s ease;
      }

      .progressive-image.loading {
        filter: blur(5px);
        transform: scale(1.02);
      }

      .progressive-image.loaded {
        filter: none;
        transform: scale(1);
      }

      /* Prevent font flash */
      .font-loading {
        visibility: hidden;
      }

      .fonts-loaded .font-loading {
        visibility: visible;
      }
    </style>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "lucide-react": "https://esm.sh/lucide-react@^0.400.0",
    "react-markdown": "https://esm.sh/react-markdown@^10.1.0",
    "swiper/react": "https://esm.sh/swiper@11/react?external=react",
    "swiper/modules": "https://esm.sh/swiper@11/modules?external=react",
    "swiper/": "https://esm.sh/swiper@^11.2.8/"
  }
}
</script>
</head>
<body class="bg-[#F9F9F7] text-[#555555] font-body">
    <div id="root"></div>

    <!-- Font loading detection -->
    <script>
      // Add fonts-loaded class when fonts are ready
      if ('fonts' in document) {
        document.fonts.ready.then(() => {
          document.documentElement.classList.add('fonts-loaded');
        });
      } else {
        // Fallback for older browsers
        setTimeout(() => {
          document.documentElement.classList.add('fonts-loaded');
        }, 100);
      }
    </script>

    <script type="module" src="/index.tsx"></script>
</body>
</html>