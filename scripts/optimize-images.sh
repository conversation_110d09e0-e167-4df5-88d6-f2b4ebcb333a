#!/bin/bash

# Image Optimization Script
# Optimizes all images in public/images/ directory

echo "🖼️  Starting image optimization..."

# Create optimized directory if it doesn't exist
mkdir -p public/images/optimized

# Function to optimize a single image
optimize_image() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    local name="${filename%.*}"
    local ext="${filename##*.}"

    # Check if optimized versions already exist and are newer than source
    local webp_file="public/images/${name}.webp"
    local optimized_file=""
    local low_file="public/images/${name}-low.jpg"

    if [[ "$ext" == "png" ]]; then
        optimized_file="public/images/${name}-optimized.png"
    elif [[ "$ext" == "jpg" || "$ext" == "jpeg" ]]; then
        optimized_file="public/images/${name}-optimized.jpg"
    fi

    # Skip if all optimized versions exist and are newer than source
    if [[ -f "$webp_file" && -f "$optimized_file" && -f "$low_file" ]]; then
        if [[ "$webp_file" -nt "$input_file" && "$optimized_file" -nt "$input_file" && "$low_file" -nt "$input_file" ]]; then
            echo "⏭️  Skipping $filename (already optimized)"
            return
        fi
    fi

    echo "📸 Optimizing: $filename"

    # Create WebP version (best compression)
    magick "$input_file" -quality 85 -strip "$webp_file"
    echo "   ✅ Created WebP: ${name}.webp"

    # Create optimized PNG/JPG
    if [[ "$ext" == "png" ]]; then
        magick "$input_file" -quality 80 -strip "$optimized_file"
        echo "   ✅ Created optimized PNG: ${name}-optimized.png"
    elif [[ "$ext" == "jpg" || "$ext" == "jpeg" ]]; then
        magick "$input_file" -quality 85 -strip "$optimized_file"
        echo "   ✅ Created optimized JPG: ${name}-optimized.jpg"
    fi

    # Create low-quality placeholder
    magick "$input_file" -resize 50x30 -quality 50 -strip "$low_file"
    echo "   ✅ Created placeholder: ${name}-low.jpg"

    # Show file sizes
    echo "   📊 File sizes:"
    ls -lh "$input_file" "$webp_file" "$optimized_file" "$low_file" 2>/dev/null | awk '{print "      " $9 ": " $5}'
    echo ""
}

# Check if ImageMagick is installed
if ! command -v magick &> /dev/null; then
    # Check if we're in a CI/CD environment (like Cloudflare)
    if [[ -n "$CF_PAGES" || -n "$GITHUB_ACTIONS" || -n "$CI" ]]; then
        echo "⚠️  ImageMagick not available in CI environment. Skipping optimization."
        echo "   Images should be optimized locally before deployment."
        echo "   Run 'npm run optimize-images' locally to optimize images."
        exit 0
    else
        echo "❌ ImageMagick not found. Please install it first:"
        echo "   macOS: brew install imagemagick"
        echo "   Ubuntu: sudo apt install imagemagick"
        echo "   Windows: Download from https://imagemagick.org/"
        exit 1
    fi
fi

# Process all images in public/images/
if [ "$1" ]; then
    # Optimize specific file
    if [ -f "public/images/$1" ]; then
        optimize_image "public/images/$1"
    else
        echo "❌ File not found: public/images/$1"
        exit 1
    fi
else
    # Optimize all images
    for file in public/images/*.{png,jpg,jpeg}; do
        if [ -f "$file" ] && [[ ! "$file" =~ -optimized\. ]] && [[ ! "$file" =~ -low\. ]]; then
            optimize_image "$file"
        fi
    done
fi

echo "🎉 Image optimization complete!"
echo ""
echo "💡 Tips:"
echo "   - Use WebP versions in your components for best performance"
echo "   - Use -low.jpg versions as placeholders"
echo "   - Use -optimized versions as fallbacks"
