import { getAssetFromKV } from '@cloudflare/kv-asset-handler'

const DEBUG = false

addEventListener('fetch', event => {
  event.respondWith(handleEvent(event).catch(err => {
    if (DEBUG) {
      return new Response(err.stack, { status: 500 })
    }
    return new Response('Internal Error', { status: 500 })
  }))
})

async function handleEvent(event) {
  const { request } = event
  const url = new URL(request.url)

  // Try to serve static assets first
  try {
    const page = await getAssetFromKV(event, DEBUG ? { cacheControl: { bypassCache: true } } : {})
    const response = new Response(page.body, page)
    
    // Add security headers
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    
    // Cache control for different file types
    const pathname = url.pathname
    if (pathname.includes('/assets/') || pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/)) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    } else if (pathname.endsWith('.json')) {
      // Don't cache JSON files (translation files)
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')
    }
    
    return response
  } catch (e) {
    // If asset not found, check if it's a file request that should return 404
    const pathname = url.pathname
    
    // Don't serve index.html for actual file requests (except .html routes)
    if (pathname.includes('.') && 
        !pathname.endsWith('.html') && 
        !pathname.endsWith('/') &&
        pathname !== '/index.html') {
      return new Response('Not Found', { status: 404 })
    }
    
    // For SPA routes (including /en/, /cs/, etc.), serve index.html
    try {
      const notFound = await getAssetFromKV(event, {
        mapRequestToAsset: req => new Request(`${new URL(req.url).origin}/index.html`, req),
      })
      return new Response(notFound.body, { 
        ...notFound, 
        status: 200,
        headers: {
          ...notFound.headers,
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=0, must-revalidate'
        }
      })
    } catch (error) {
      return new Response('Not Found', { status: 404 })
    }
  }
}
