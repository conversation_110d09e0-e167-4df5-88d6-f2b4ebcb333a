# Complete Cloudflare Workers Deployment Guide

This guide covers the complete setup for deploying React/Vite SPAs to Cloudflare Workers with performance optimizations.

## 🚀 Quick Setup

### 1. Cloudflare Dashboard Settings
- **Build command**: `npm run build:ci`
- **Deploy command**: `npx wrangler deploy`
- **Build output directory**: `dist`

### 2. Why These Settings?
- **`build:ci`**: Skips image optimization in CI (no ImageMagick dependency)
- **Pre-optimized images**: Uses optimized images from local development
- **Reliable deployment**: No build failures due to missing dependencies

## 📁 Required Files

### 1. `wrangler.toml`
```toml
name = "your-project-name"
main = "workers-site/index.js"
compatibility_date = "2024-01-01"

[site]
bucket = "./dist"

# Custom domain configuration
routes = [
  { pattern = "yourdomain.com/*", zone_name = "yourdomain.com" },
  { pattern = "www.yourdomain.com/*", zone_name = "yourdomain.com" }
]
```

### 2. `workers-site/index.js`
```javascript
import { getAssetFromKV } from '@cloudflare/kv-asset-handler'

const DEBUG = false

addEventListener('fetch', event => {
  event.respondWith(handleEvent(event).catch(err => {
    if (DEBUG) {
      return new Response(err.stack, { status: 500 })
    }
    return new Response('Internal Error', { status: 500 })
  }))
})

async function handleEvent(event) {
  const { request } = event
  const url = new URL(request.url)

  // Try to serve static assets first
  try {
    const page = await getAssetFromKV(event, DEBUG ? { cacheControl: { bypassCache: true } } : {})
    const response = new Response(page.body, page)

    // Add security headers
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')

    // Cache control for different file types
    const pathname = url.pathname
    if (pathname.includes('/assets/') || pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/)) {
      response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
    } else if (pathname.endsWith('.json')) {
      // Don't cache JSON files (translation files)
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')
    }

    return response
  } catch (e) {
    // If asset not found, check if it's a file request that should return 404
    const pathname = url.pathname

    // Don't serve index.html for actual file requests (except .html routes)
    if (pathname.includes('.') &&
        !pathname.endsWith('.html') &&
        !pathname.endsWith('/') &&
        pathname !== '/index.html') {
      return new Response('Not Found', { status: 404 })
    }

    // For SPA routes (including /en/, /cs/, etc.), serve index.html
    try {
      const notFound = await getAssetFromKV(event, {
        mapRequestToAsset: req => new Request(`${new URL(req.url).origin}/index.html`, req),
      })
      return new Response(notFound.body, {
        ...notFound,
        status: 200,
        headers: {
          ...notFound.headers,
          'Content-Type': 'text/html; charset=utf-8',
          'Cache-Control': 'public, max-age=0, must-revalidate'
        }
      })
    } catch (error) {
      return new Response('Not Found', { status: 404 })
    }
  }
}
```

### 3. `package.json` Dependencies
```json
{
  "devDependencies": {
    "@cloudflare/kv-asset-handler": "^0.4.0",
    "wrangler": "^4.19.1"
  },
  "scripts": {
    "build:ci": "vite build",
    "deploy": "npm run build && npx wrangler deploy"
  }
}
```

## How It Works

### Local Development:
```bash
npm run build          # Optimizes images + builds
npm run optimize-images # Manual optimization
npm run dev            # Development server
```

### CI/CD Deployment:
```bash
npm run build:ci       # Builds without optimization (used by Cloudflare)
```

### The Script Logic:
1. **Local**: Runs ImageMagick optimization if available
2. **CI/CD**: Detects CI environment and skips optimization gracefully
3. **Pre-optimized images**: Uses already optimized images from local development

## Workflow

1. **Develop locally**: Images get optimized automatically during `npm run build`
2. **Commit optimized images**: All `.webp`, `-optimized.*`, and `-low.jpg` files
3. **Deploy**: Cloudflare uses pre-optimized images, skips optimization step
4. **Result**: Fast deployment with optimized images

## Files to Commit

Make sure these optimized files are committed to your repository:
```
public/images/
├── hero.webp (146KB)
├── hero-optimized.png (2.4MB)
├── hero-low.jpg (565B)
├── martin.webp (617KB)
├── martin-optimized.jpg (896KB)
├── martin-low.jpg (490B)
├── martin2.webp (585KB)
├── martin2-optimized.png (4.3MB)
├── martin2-low.jpg (557B)
├── craniocare-jablonec.webp (126KB)
├── craniocare-jablonec-optimized.jpg (175KB)
├── craniocare-jablonec-low.jpg (552B)
├── craniocare-praha.webp (101KB)
├── craniocare-praha-optimized.jpg (176KB)
└── craniocare-praha-low.jpg (686B)
```

## Benefits

✅ **Fast local development** with automatic optimization  
✅ **Fast CI/CD deployment** without ImageMagick dependency  
✅ **Optimized images** in production (pre-optimized locally)  
✅ **No build failures** in CI environments  
✅ **Best of both worlds** - optimization + reliable deployment  

## Alternative: GitHub Actions Pre-optimization

If you want to optimize images in CI, you could set up GitHub Actions:

```yaml
# .github/workflows/optimize-images.yml
name: Optimize Images
on:
  push:
    paths: ['public/images/**']
jobs:
  optimize:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install ImageMagick
        run: sudo apt-get install imagemagick
      - name: Optimize Images
        run: npm run optimize-images
      - name: Commit optimized images
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add public/images/
          git commit -m "Auto-optimize images" || exit 0
          git push
```

But the current solution (local optimization + commit) is simpler and more reliable.

## 🎯 Complete Setup Checklist

### First-time Setup:
1. ✅ Install dependencies: `npm install`
2. ✅ Login to Cloudflare: `npx wrangler login`
3. ✅ Create `wrangler.toml` with your project name
4. ✅ Create `workers-site/index.js` file
5. ✅ Update Cloudflare dashboard build settings
6. ✅ Deploy: `npm run deploy`

### For Performance Optimization:
1. ✅ Install ImageMagick locally: `brew install imagemagick`
2. ✅ Run optimization: `npm run optimize-images`
3. ✅ Commit optimized images to repository
4. ✅ Deploy with pre-optimized images

### Troubleshooting:
- **Authentication issues**: `npx wrangler logout && npx wrangler login`
- **Build failures**: Use `npm run build:ci` in Cloudflare settings
- **Domain issues**: Check zone_name matches your domain exactly
- **Missing images**: Ensure optimized images are committed to repo

## 🚀 What You Get

✅ **Fast SPA routing** for React apps
✅ **Optimized images** (60-94% smaller)
✅ **Global CDN** distribution
✅ **Automatic HTTPS**
✅ **Custom domain** support
✅ **Reliable deployment** (no CI dependencies)
✅ **Professional performance** (excellent Core Web Vitals)

## 💰 Cost
- **Free tier**: 100,000 requests/day
- **Paid tier**: $5/month for 10M requests
- **No bandwidth charges**
- **No storage charges**
