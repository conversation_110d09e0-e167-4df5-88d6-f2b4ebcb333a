
import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import HeroSection from './components/HeroSection';
import PainPointsSection from './components/PainPointsSection';
import WhatIOfferSection from './components/WhatIOfferSection';
import WhoIsThisForSection from './components/WhoIsThisForSection';
import TrainingCredentialsSection from './components/TrainingCredentialsSection';
import TestimonialsSection from './components/TestimonialsSection';
import LocationsSection from './components/LocationsSection';
import PricingSection from './components/PricingSection';
import AboutBriefSection from './components/AboutBriefSection';
import SecondaryCTASection from './components/SecondaryCTASection';
import MyStorySection from './components/MyStorySection';
import FAQSection from './components/FAQSection';
import FinalCTASection from './components/FinalCTASection';
import Footer from './components/Footer';
import Button from './components/Button';
import LoadingAnimation from './components/LoadingAnimation';
import { COLORS, bookUrl } from './constants';
import { LanguageProvider, useTranslation } from './contexts/LanguageContext';

const AppContent: React.FC = () => {
  const { t, isLoading: translationsLoading } = useTranslation();
  const [isAppVisible, setIsAppVisible] = useState(false);

  useEffect(() => {
    if (!translationsLoading) {
      // Translations are loaded, start transition to show app
      const timer = setTimeout(() => {
        setIsAppVisible(true);
      }, 100); // Short delay to ensure loader is seen, then fade app in
      return () => clearTimeout(timer);
    } else {
      // If translations start loading again (e.g. lang change), prepare to hide app content
      // to show loader, by resetting isAppVisible.
      setIsAppVisible(false);
    }
  }, [translationsLoading]);

  const scrollToPricing = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();
    const targetElement = document.getElementById('pricing');
    if (targetElement) {
      targetElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Show loader if translations are loading OR if app is not yet marked as visible
  if (translationsLoading || !isAppVisible) {
    return <LoadingAnimation />;
  }

  // Translations loaded AND app is set to be visible
  return (
    <div className={`transition-opacity duration-500 ease-in-out ${isAppVisible ? 'opacity-100' : 'opacity-0'}`}>
      <Header />
      <main>
        <HeroSection />
        <PainPointsSection />
        <WhatIOfferSection />
        <WhoIsThisForSection />
        <TrainingCredentialsSection />
        <TestimonialsSection />
        <LocationsSection />
        <PricingSection />
        <AboutBriefSection />
        <SecondaryCTASection />
        <MyStorySection />
        <FAQSection />
        <FinalCTASection />
      </main>
      <Footer />
      
      {/* Sticky "Book Now" button for mobile */}
      <div className="md:hidden fixed bottom-4 right-4 z-40">
        <Button 
          // onClick={scrollToPricing} 
          variant="primary" 
          className={`!rounded-full !px-6 !py-3 shadow-xl bg-[${COLORS.sageGreen}] text-white`}
          aria-label={t('button.bookNow')}
          href={`${bookUrl}`}
        >
          {t('button.bookNow')}
        </Button>
      </div>
    </div>
  );
}

function App() {
  return (
    <LanguageProvider>
      <AppContent />
    </LanguageProvider>
  );
}

export default App;