
import React from 'react';

export interface NavItem {
  name: string;
  href: string;
}

export interface PainPoint {
  icon: React.ReactElement;
  title: string;
  description: string;
}

export interface ClientProfile {
  icon: React.ReactElement;
  title: string;
  description: string;
}

export interface Testimonial {
  photoUrl: string;
  name: string;
  title?: string;
  quote: string;
}

export interface Location {
  name: string;
  address: string;
  details: string;
  highlights: string[];
  mapImageUrl: string;
  mapLink: string;
}

export interface PricingPackage {
  name: string;
  duration?: string;
  details: string[];
  price: string;
  isMostPopular?: boolean;
  ctaText: string;
  ctaLink: string;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface IconProps {
  className?: string;
}
