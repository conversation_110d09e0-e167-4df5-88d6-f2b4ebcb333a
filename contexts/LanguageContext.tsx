
import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';

interface Translations {
  [key: string]: any; // Allow any value, including nested objects, arrays, strings, etc.
}

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string, replacements?: { [key: string]: string | number }) => any; // Return type 'any'
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const supportedLanguages = ['en', 'cs'];
const defaultLanguage = 'en';

const ogImageRelativePath = '/og-image.png'; // Updated path

// Helper function to set or create a meta tag
const setMetaTag = (nameOrProperty: string, content: string, isProperty: boolean = false) => {
  const selector = isProperty ? `meta[property="${nameOrProperty}"]` : `meta[name="${nameOrProperty}"]`;
  let tag = document.head.querySelector(selector) as HTMLMetaElement | null;
  if (!tag) {
    tag = document.createElement('meta');
    if (isProperty) {
      tag.setAttribute('property', nameOrProperty);
    } else {
      tag.setAttribute('name', nameOrProperty);
    }
    document.head.appendChild(tag);
  }
  if (content) {
    tag.setAttribute('content', content);
  } else {
    // If content is empty/null, remove the tag or its content. Removing tag for cleanliness.
    if (tag) {
        // tag.removeAttribute('content'); 
        tag.remove();
    }
  }
};

// Helper function to set or create a link tag
const setLinkTag = (rel: string, href: string) => {
  let tag = document.head.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement | null;
  if (!tag) {
    tag = document.createElement('link');
    tag.setAttribute('rel', rel);
    document.head.appendChild(tag);
  }
  if (href) {
    tag.setAttribute('href', href);
  } else {
    if (tag) {
        // tag.removeAttribute('href');
        tag.remove();
    }
  }
};

// Helper function to set or create a script tag for JSON-LD
const setJsonLdScript = (jsonData: object) => {
  const scriptId = 'json-ld-structured-data';
  let scriptTag = document.getElementById(scriptId) as HTMLScriptElement | null;
  if (!scriptTag) {
    scriptTag = document.createElement('script');
    scriptTag.type = 'application/ld+json';
    scriptTag.id = scriptId;
    document.head.appendChild(scriptTag);
  }
  scriptTag.textContent = JSON.stringify(jsonData);
};


const getPathFromCurrentLocation = (): string => {
  let path;
  try {
    const url = new URL(window.location.href);
    path = url.pathname;
  } catch (e) {
    console.warn("LanguageContext: Error parsing window.location.href, falling back to window.location.pathname", e);
    path = window.location.pathname;
  }
  return path;
};

const getPathWithoutLangPrefix = (pathname: string): string => {
  let path = pathname;
  const lowerPath = path.toLowerCase();

  for (const lang of supportedLanguages) {
    if (lowerPath.startsWith(`/${lang}/`)) {
      path = path.substring(lang.length + 1); 
      break;
    }
    if (lowerPath === `/${lang}`) {
      path = '/'; 
      break;
    }
  }

  if (path.match(/^\/(https?):/i)) {
    try {
      let embeddedFullUrlString = path.substring(1);
      if (embeddedFullUrlString.startsWith("http:") && !embeddedFullUrlString.startsWith("http://")) {
        embeddedFullUrlString = "http://" + embeddedFullUrlString.substring(5);
      } else if (embeddedFullUrlString.startsWith("https:") && !embeddedFullUrlString.startsWith("https://")) {
        embeddedFullUrlString = "https://" + embeddedFullUrlString.substring(6);
      }

      if (embeddedFullUrlString.toLowerCase().startsWith("http://") || embeddedFullUrlString.toLowerCase().startsWith("https://")) {
        const parsedEmbeddedUrl = new URL(embeddedFullUrlString);
        const strippedPath = parsedEmbeddedUrl.pathname;
        console.log(`LanguageContext: Stripped embedded URL from path. Original segment: "${path}", Corrected Full URL: "${embeddedFullUrlString}", Stripped to: "${strippedPath}"`);
        return strippedPath;
      }
    } catch (e) {
      console.warn(`LanguageContext: Failed to parse/strip embedded URL from path segment: "${path}" (original or corrected). Error:`, e);
    }
  }
  return path;
};


const getInitialLanguage = (): string => {
  const currentPathname = getPathFromCurrentLocation();
  
  const firstSegment = currentPathname.split('/')[1]?.toLowerCase();
  if (firstSegment && supportedLanguages.includes(firstSegment)) {
    console.log(`LanguageContext: Language from URL path: "${firstSegment}"`);
    return firstSegment;
  }

  const browserLangs = navigator.languages || [navigator.language];
  for (const lang of browserLangs) {
    const primaryLang = lang.split('-')[0].toLowerCase();
    if (supportedLanguages.includes(primaryLang)) {
      console.log(`LanguageContext: Language from browser (navigator.languages): "${primaryLang}" (derived from "${lang}")`);
      return primaryLang;
    }
  }

  console.log(`LanguageContext: Defaulting to language: "${defaultLanguage}"`);
  return defaultLanguage;
};

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<string>(getInitialLanguage);
  const [translations, setTranslations] = useState<Translations>({});
  const [isLoading, setIsLoading] = useState(true);
  const isBlobOrigin = window.location.protocol === 'blob:';

  useEffect(() => {
    const initialPathname = getPathFromCurrentLocation();
    const detectedLangFromPath = initialPathname.split('/')[1]?.toLowerCase();

    if (!detectedLangFromPath || !supportedLanguages.includes(detectedLangFromPath)) {
      const baseAppPath = getPathWithoutLangPrefix(initialPathname); 
      const safeBaseAppPath = baseAppPath.startsWith('/') ? baseAppPath : `/${baseAppPath}`;
      
      let newHistoryPath = `/${language}${safeBaseAppPath}${window.location.search}${window.location.hash}`;
      newHistoryPath = newHistoryPath.replace(/\/\//g, '/'); 
      
      console.log(`LanguageContext: Initial URL normalization. Current raw pathname: "${initialPathname}", Detected language: "${language}", Base app path: "${safeBaseAppPath}", New history path: "${newHistoryPath}"`);
      if (!isBlobOrigin && window.location.pathname !== newHistoryPath) { 
          window.history.replaceState({ path: newHistoryPath }, '', newHistoryPath);
      } else if (isBlobOrigin) {
        console.log("LanguageContext: Running in blob origin. Initial URL replaceState skipped.");
      }
    }

    const loadTranslations = async () => {
      if (!language) {
        console.log("LanguageContext: Language state is empty, skipping translation load.");
        setIsLoading(false);
        return;
      }
      console.log(`LanguageContext: Attempting to load translations for language: "${language}"`);
      console.log(`LanguageContext: document.baseURI is: ${document.baseURI}, window.location.href is: ${window.location.href}`);
      setIsLoading(true);
      let translationsToSet: Translations | null = {};
      let loadedSuccessfully = false;

      const cacheBuster = Date.now();

      try {
        const primaryLocalePath = `/locales/${language}.json?v=${cacheBuster}`;
        // Use window.location.href as the base for new URL() for robustness
        const primaryLocaleUrl = new URL(primaryLocalePath, window.location.href).href;
        console.log(`LanguageContext: Constructed primary locale URL for ${language}: ${primaryLocaleUrl}`);
        const response = await fetch(primaryLocaleUrl);

        if (!response.ok) {
          console.error(`LanguageContext: Failed to load translations for "${language}" from ${primaryLocaleUrl}. Status: ${response.status}. Attempting fallback to "${defaultLanguage}".`);
          if (language !== defaultLanguage) {
            const fallbackLocalePath = `/locales/${defaultLanguage}.json?v=${cacheBuster}`;
            const fallbackLocaleUrl = new URL(fallbackLocalePath, window.location.href).href;
            console.log(`LanguageContext: Constructed fallback locale URL for ${defaultLanguage}: ${fallbackLocaleUrl}`);
            const fallbackResponse = await fetch(fallbackLocaleUrl);

            if (fallbackResponse.ok) {
              const jsonData = await fallbackResponse.json();
              if (typeof jsonData === 'object' && jsonData !== null) {
                translationsToSet = jsonData;
              } else {
                translationsToSet = {}; 
                console.error(`LanguageContext: Fallback translations for "${defaultLanguage}" from ${fallbackLocaleUrl} is null or not an object.`);
              }
              const currentPathForFallback = getPathFromCurrentLocation();
              const baseAppPathForFallback = getPathWithoutLangPrefix(currentPathForFallback);
              const safeBaseAppPathFallback = baseAppPathForFallback.startsWith('/') ? baseAppPathForFallback : `/${baseAppPathForFallback}`;
              let fallbackUrl = `/${defaultLanguage}${safeBaseAppPathFallback}${window.location.search}${window.location.hash}`;
              fallbackUrl = fallbackUrl.replace(/\/\//g, '/');
              
              setLanguageState(defaultLanguage); 
              if (!isBlobOrigin && window.location.pathname !== fallbackUrl) {
                window.history.replaceState({ path: fallbackUrl }, '', fallbackUrl);
              } else if (isBlobOrigin) {
                console.log("LanguageContext: Running in blob origin. Fallback URL replaceState skipped.");
              }
              loadedSuccessfully = true;
              console.log(`LanguageContext: Successfully loaded fallback "${defaultLanguage}" translations from ${fallbackLocaleUrl}. Language state and URL updated (if not blob).`);
            } else {
               console.error(`LanguageContext: Failed to load fallback "${defaultLanguage}" translations from ${fallbackLocaleUrl}. Status: ${fallbackResponse.status}`);
            }
          }
        } else {
          const jsonData = await response.json();
          if (typeof jsonData === 'object' && jsonData !== null) {
            translationsToSet = jsonData;
          } else {
            translationsToSet = {}; 
            console.error(`LanguageContext: Translations for "${language}" from ${primaryLocaleUrl} is null or not an object.`);
          }
          loadedSuccessfully = true;
          console.log(`LanguageContext: Successfully loaded translations for "${language}" from ${primaryLocaleUrl}.`);
        }
      } catch (error: any) {
        console.error(`LanguageContext: Error loading translation file for "${language}":`, error.message);
         if (language !== defaultLanguage) {
            console.log(`LanguageContext: Falling back to "${defaultLanguage}" translations due to error.`);
            try {
                const catchFallbackLocalePath = `/locales/${defaultLanguage}.json?v=${cacheBuster}`;
                const catchFallbackLocaleUrl = new URL(catchFallbackLocalePath, window.location.href).href;
                console.log(`LanguageContext: Constructed catch fallback locale URL for ${defaultLanguage}: ${catchFallbackLocaleUrl}`);
                const fallbackResponse = await fetch(catchFallbackLocaleUrl);

                 if (fallbackResponse.ok) {
                    const jsonData = await fallbackResponse.json();
                    if (typeof jsonData === 'object' && jsonData !== null) {
                      translationsToSet = jsonData;
                    } else {
                      translationsToSet = {};
                      console.error(`LanguageContext: Fallback (catch) translations for "${defaultLanguage}" from ${catchFallbackLocaleUrl} is null or not an object.`);
                    }
                    const currentPathForCatchFallback = getPathFromCurrentLocation();
                    const baseAppPathForCatch = getPathWithoutLangPrefix(currentPathForCatchFallback);
                    const safeBaseAppPathCatch = baseAppPathForCatch.startsWith('/') ? baseAppPathForCatch : `/${baseAppPathForCatch}`;
                    let fallbackUrlCatch = `/${defaultLanguage}${safeBaseAppPathCatch}${window.location.search}${window.location.hash}`;
                    fallbackUrlCatch = fallbackUrlCatch.replace(/\/\//g, '/');

                    setLanguageState(defaultLanguage);
                    if (!isBlobOrigin && window.location.pathname !== fallbackUrlCatch) {
                        window.history.replaceState({ path: fallbackUrlCatch }, '', fallbackUrlCatch);
                    } else if (isBlobOrigin) {
                        console.log("LanguageContext: Running in blob origin. Fallback URL replaceState (catch) skipped.");
                    }
                    loadedSuccessfully = true;
                    console.log(`LanguageContext: Successfully loaded fallback "${defaultLanguage}" translations on catch from ${catchFallbackLocaleUrl}. Language state and URL updated (if not blob).`);
                } else {
                    console.error(`LanguageContext: Failed to load fallback "${defaultLanguage}" translations on catch from ${catchFallbackLocaleUrl}. Status: ${fallbackResponse.status}`);
                }
            } catch (e: any) {
                console.error(`LanguageContext: Error loading fallback "${defaultLanguage}" translations on catch:`, e.message);
            }
        }
      }
      
      setTranslations(translationsToSet || {}); 
      setIsLoading(false);
      if (!loadedSuccessfully && (!translationsToSet || Object.keys(translationsToSet).length === 0)) {
        console.warn(`LanguageContext: Translations for "${language}" (and potential fallback) are empty or failed to load properly. UI might show keys.`);
      }
    };
    loadTranslations();
  }, [language, isBlobOrigin]);

  const setLanguage = (newLang: string) => {
    if (supportedLanguages.includes(newLang) && newLang !== language) {
      console.log(`LanguageContext: Manually setting language to: "${newLang}"`);
      
      const currentPathname = getPathFromCurrentLocation();
      const baseAppPath = getPathWithoutLangPrefix(currentPathname); 
      const safeBaseAppPath = baseAppPath.startsWith('/') ? baseAppPath : `/${baseAppPath}`;
      
      let newHistoryPath = `/${newLang}${safeBaseAppPath}${window.location.search}${window.location.hash}`;
      newHistoryPath = newHistoryPath.replace(/\/\//g, '/');

      setLanguageState(newLang); 
      if (!isBlobOrigin && window.location.pathname !== newHistoryPath) {
        window.history.pushState({ path: newHistoryPath }, '', newHistoryPath);
        console.log(`LanguageContext: URL updated by manual switch: ${newHistoryPath}`);
      } else if (isBlobOrigin) {
        console.log(`LanguageContext: Running in blob origin. URL pushState skipped for manual language switch.`);
      } else {
        console.log(`LanguageContext: Path already correct after language switch intent, no pushState needed.`);
      }
    } else if (newLang === language) {
      console.log(`LanguageContext: Language already set to "${newLang}". No change.`);
    } else {
      console.warn(`LanguageContext: Attempted to set unsupported language: "${newLang}"`);
    }
  };

  const t = (key: string, replacements?: { [key: string]: string | number }): any => {
    // If we're still loading, don't return anything yet - wait for translations
    if (isLoading) {
      return key;
    }

    // If we're not loading but have no translations, return the key as fallback
    if (Object.keys(translations).length === 0) {
      return key;
    }

    const keys = key.split('.');
    let result: any = translations;
    for (const k of keys) {
      result = result?.[k];
      if (result === undefined) {
        return key; 
      }
    }

    if (typeof result === 'string') {
      if (replacements) {
        return Object.entries(replacements).reduce((acc, [placeholder, value]) => {
          return acc.replace(new RegExp(`{{${placeholder}}}`, 'g'), String(value));
        }, result);
      }
      return result;
    }
    
    // If result is not undefined and not a string, it's a complex object (e.g., array or object)
    if (result !== undefined) {
      return result;
    }

    return key; // Fallback if key somehow resolves to undefined at the end of a valid path
  };
  
  useEffect(() => {
    if (language && !isLoading && Object.keys(translations).length > 0) {
      document.documentElement.lang = language;
      console.log(`LanguageContext: Set document.documentElement.lang to "${language}"`);

      // Use window.location.origin for pageOrigin, handle "null" for file:// protocol
      let pageOrigin = window.location.origin;
      if (pageOrigin === "null" || !pageOrigin) { // "null" (string) for file:, !pageOrigin for safety
          console.warn("LanguageContext: window.location.origin is null or undefined. SEO URLs may be incomplete for file:// protocol or unusual environments.");
          pageOrigin = ''; // Avoid "null" string in constructed URLs; they'll become root-relative.
      }


      const currentPathname = getPathFromCurrentLocation();
      const baseAppPath = getPathWithoutLangPrefix(currentPathname);
      const safeBaseAppPath = baseAppPath.startsWith('/') ? baseAppPath : `/${baseAppPath}`;
      const pageSpecificSubPath = `${safeBaseAppPath}${window.location.search}${window.location.hash}`.replace(/\/\//g, '/');
      
      const canonicalUrl = `${pageOrigin}/${language}${pageSpecificSubPath}`.replace(`${pageOrigin}//`, `${pageOrigin}/`); // Ensure single slash after origin
      const ogImageAbsoluteUrl = `${pageOrigin}${ogImageRelativePath}`.replace(`${pageOrigin}//`, `${pageOrigin}/`);


      // Update Title
      document.title = t('seo.main.title') || t('site.name');

      // Update Meta Description
      setMetaTag('description', t('seo.main.description'));

      // Update Meta Keywords
      setMetaTag('keywords', t('seo.main.keywords'));
      
      // Update Canonical URL
      setLinkTag('canonical', canonicalUrl);

      // Update Open Graph Tags
      setMetaTag('og:title', t('seo.main.title') || t('site.name'), true);
      setMetaTag('og:description', t('og.description'), true);
      setMetaTag('og:url', canonicalUrl, true);
      setMetaTag('og:image', ogImageAbsoluteUrl, true);
      setMetaTag('og:type', 'website', true);
      const currentOgLocale = language === 'cs' ? 'cs_CZ' : 'en_US';
      setMetaTag('og:locale', currentOgLocale, true);
      
      // Add og:locale:alternate
      document.querySelectorAll('meta[property="og:locale:alternate"]').forEach(tag => tag.remove());
      supportedLanguages.forEach(lang => {
        if (lang !== language) {
          const alternateOgLocale = lang === 'cs' ? 'cs_CZ' : 'en_US';
          const newMeta = document.createElement('meta');
          newMeta.setAttribute('property', 'og:locale:alternate');
          newMeta.setAttribute('content', alternateOgLocale);
          document.head.appendChild(newMeta);
        }
      });

      setMetaTag('og:site_name', t('og.siteName') || t('site.name'), true);

      // Update Twitter Card Tags
      setMetaTag('twitter:card', 'summary_large_image');
      setMetaTag('twitter:title', t('seo.main.title') || t('site.name'));
      setMetaTag('twitter:description', t('seo.main.description'));
      setMetaTag('twitter:image', ogImageAbsoluteUrl);

      // Manage hreflang links
      document.querySelectorAll('link[rel="alternate"][hreflang]').forEach(link => link.remove());
      
      supportedLanguages.forEach(lang => {
        const link = document.createElement('link');
        link.rel = 'alternate';
        link.hreflang = lang;
        let langPath = `/${lang}${pageSpecificSubPath}`;
        langPath = langPath.replace(/\/\//g, '/');
        link.href = `${pageOrigin}${langPath}`.replace(`${pageOrigin}//`, `${pageOrigin}/`);
        document.head.appendChild(link);
      });
      
      const xDefaultLink = document.createElement('link');
      xDefaultLink.rel = 'alternate';
      xDefaultLink.hreflang = 'x-default';
      let xDefaultPath = `/${defaultLanguage}${pageSpecificSubPath}`; // Default language link
      xDefaultPath = xDefaultPath.replace(/\/\//g, '/');
      xDefaultLink.href = `${pageOrigin}${xDefaultPath}`.replace(`${pageOrigin}//`, `${pageOrigin}/`);
      document.head.appendChild(xDefaultLink);
      console.log(`LanguageContext: Updated hreflang tags for language "${language}" and sub-path "${pageSpecificSubPath}". Origin for hreflang: ${pageOrigin}`);

      // JSON-LD Structured Data
      const structuredData = {
        "@context": "https://schema.org",
        "@type": "MedicalTherapy",
        "name": t('og.siteName') || t('site.name'),
        "description": t('seo.main.description'),
        "image": ogImageAbsoluteUrl,
        "url": canonicalUrl,
        "provider": {
          "@type": "Person",
          "name": t('site.author')
        },
        "availableService": [
           {
            "@type": "MedicalProcedure",
            "name": "Craniosacral Therapy Session",
            "description": t('whatIOffer.description'),
            "provider": {
                "@type": "Person",
                "name": t('site.author')
            }
           }
        ],
        // Additional properties if relevant
         "contactPoint": [
            { "@type": "ContactPoint", "telephone": t('footer.phone').replace(/\s|\(|\)/g, ''), "contactType": "customer support" , "email": t('footer.email') }
         ],
         // Example of adding locations, needs full address details to be useful for LocalBusiness type
         // If practice has fixed locations, consider changing @type to "HealthAndBeautyBusiness" or "MedicalBusiness"
         // and adding 'address' property. For now, MedicalTherapy is service-focused.
      };
      setJsonLdScript(structuredData);

    }
  }, [language, isLoading, translations, t]); // Added t to dependency array

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isLoading }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useTranslation = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useTranslation must be used within a LanguageProvider');
  }
  return context;
};