Set up performance optimizations using COMPLETE_PERFORMANCE_OPTIMIZATION_TEMPLATE.md:

# Complete Performance Optimization Template

This is a comprehensive template for setting up automatic performance optimizations in any React/Vite project. Just provide this file to get all optimizations configured automatically.

## 🚀 What This Template Provides

### ✅ **Automatic Image Optimization**
- WebP conversion with 60-94% size reduction
- Tiny placeholder generation (~500B each)
- Smart caching (only processes changed images)
- Progressive loading with blur-to-sharp transitions
- Intersection Observer lazy loading
- WebP detection with automatic fallbacks

### ✅ **Font Loading Optimization**
- Eliminates FOUT (Flash of Unstyled Text)
- System font fallbacks for instant display
- Critical font preloading
- Font loading detection

### ✅ **Critical Resource Preloading**
- Hero images preloaded for instant display
- Above-the-fold content prioritized
- Smart resource hints

### ✅ **Automated Build Integration**
- Runs automatically during build
- Smart optimization (skips unchanged files)
- Development vs production modes

## 📁 Required Files Structure

```
project/
├── scripts/
│   └── optimize-images.sh
├── components/
│   ├── OptimizedImage.tsx
│   └── ProgressiveBackgroundImage.tsx
├── public/
│   ├── images/
│   └── _headers
├── package.json (updated scripts)
├── index.html (optimized)
└── workers-site/index.js (if using Cloudflare Workers)
```

## 🛠️ Implementation Checklist

### 1. **Package.json Scripts**
```json
{
  "scripts": {
    "dev": "vite",
    "build": "npm run optimize-images && vite build",
    "build:ci": "vite build",
    "build:fast": "vite build",
    "preview": "vite preview",
    "deploy": "npm run build && npx wrangler deploy",
    "cf:dev": "npm run build && npx wrangler dev",
    "optimize-images": "./scripts/optimize-images.sh",
    "optimize-image": "./scripts/optimize-images.sh"
  }
}
```

### 2. **Image Optimization Script** (`scripts/optimize-images.sh`)

**Features:**
- Smart optimization with timestamp checking
- Multiple format generation (WebP, optimized originals, placeholders)
- Automatic quality settings (WebP: 85%, PNG: 80%, JPG: 85%)
- Tiny placeholder generation (50x30px, 50% quality)
- File size reporting and comparison
- Error handling and fallbacks
- Skips already optimized files (performance boost)

**Complete Script:**
```bash
#!/bin/bash

# Image Optimization Script
# Optimizes all images in public/images/ directory

echo "🖼️  Starting image optimization..."

# Create optimized directory if it doesn't exist
mkdir -p public/images/optimized

# Function to optimize a single image
optimize_image() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    local name="${filename%.*}"
    local ext="${filename##*.}"

    # Check if optimized versions already exist and are newer than source
    local webp_file="public/images/${name}.webp"
    local optimized_file=""
    local low_file="public/images/${name}-low.jpg"

    if [[ "$ext" == "png" ]]; then
        optimized_file="public/images/${name}-optimized.png"
    elif [[ "$ext" == "jpg" || "$ext" == "jpeg" ]]; then
        optimized_file="public/images/${name}-optimized.jpg"
    fi

    # Skip if all optimized versions exist and are newer than source
    if [[ -f "$webp_file" && -f "$optimized_file" && -f "$low_file" ]]; then
        if [[ "$webp_file" -nt "$input_file" && "$optimized_file" -nt "$input_file" && "$low_file" -nt "$input_file" ]]; then
            echo "⏭️  Skipping $filename (already optimized)"
            return
        fi
    fi

    echo "📸 Optimizing: $filename"

    # Create WebP version (best compression)
    magick "$input_file" -quality 85 -strip "$webp_file"
    echo "   ✅ Created WebP: ${name}.webp"

    # Create optimized PNG/JPG
    if [[ "$ext" == "png" ]]; then
        magick "$input_file" -quality 80 -strip "$optimized_file"
        echo "   ✅ Created optimized PNG: ${name}-optimized.png"
    elif [[ "$ext" == "jpg" || "$ext" == "jpeg" ]]; then
        magick "$input_file" -quality 85 -strip "$optimized_file"
        echo "   ✅ Created optimized JPG: ${name}-optimized.jpg"
    fi

    # Create low-quality placeholder
    magick "$input_file" -resize 50x30 -quality 50 -strip "$low_file"
    echo "   ✅ Created placeholder: ${name}-low.jpg"

    # Show file sizes
    echo "   📊 File sizes:"
    ls -lh "$input_file" "$webp_file" "$optimized_file" "$low_file" 2>/dev/null | awk '{print "      " $9 ": " $5}'
    echo ""
}

# Check if ImageMagick is installed
if ! command -v magick &> /dev/null; then
    # Check if we're in a CI/CD environment (like Cloudflare)
    if [[ -n "$CF_PAGES" || -n "$GITHUB_ACTIONS" || -n "$CI" ]]; then
        echo "⚠️  ImageMagick not available in CI environment. Skipping optimization."
        echo "   Images should be optimized locally before deployment."
        echo "   Run 'npm run optimize-images' locally to optimize images."
        exit 0
    else
        echo "❌ ImageMagick not found. Please install it first:"
        echo "   macOS: brew install imagemagick"
        echo "   Ubuntu: sudo apt install imagemagick"
        echo "   Windows: Download from https://imagemagick.org/"
        exit 1
    fi
fi

# Process all images in public/images/
if [ "$1" ]; then
    # Optimize specific file
    if [ -f "public/images/$1" ]; then
        optimize_image "public/images/$1"
    else
        echo "❌ File not found: public/images/$1"
        exit 1
    fi
else
    # Optimize all images
    for file in public/images/*.{png,jpg,jpeg}; do
        if [ -f "$file" ] && [[ ! "$file" =~ -optimized\. ]] && [[ ! "$file" =~ -low\. ]]; then
            optimize_image "$file"
        fi
    done
fi

echo "🎉 Image optimization complete!"
echo ""
echo "💡 Tips:"
echo "   - Use WebP versions in your components for best performance"
echo "   - Use -low.jpg versions as placeholders"
echo "   - Use -optimized versions as fallbacks"
```

**Make executable:** `chmod +x scripts/optimize-images.sh`

### 3. **React Components**

**Features:**
- **OptimizedImage**: For regular images with progressive loading
- **ProgressiveBackgroundImage**: For background images (hero sections)
- Intersection Observer integration (lazy loading)
- WebP detection and automatic fallbacks
- Smooth blur-to-sharp loading transitions
- Priority loading for above-the-fold images
- Error handling for failed image loads

**OptimizedImage Component** (`components/OptimizedImage.tsx`):
```tsx
import React, { useState, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  webpSrc?: string;
  lowQualitySrc?: string;
  priority?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  style,
  webpSrc,
  lowQualitySrc,
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || src);

  useEffect(() => {
    if (!priority) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      const element = document.getElementById(`img-${src.replace(/[^a-zA-Z0-9]/g, '')}`);
      if (element) {
        observer.observe(element);
      }

      return () => observer.disconnect();
    }
  }, [priority, src]);

  useEffect(() => {
    if (isInView && !isLoaded) {
      const img = new Image();

      // Try WebP first if supported and available
      const supportsWebP = () => {
        const canvas = document.createElement('canvas');
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      };

      const finalSrc = (webpSrc && supportsWebP()) ? webpSrc : src;

      img.onload = () => {
        setCurrentSrc(finalSrc);
        setIsLoaded(true);
      };

      img.onerror = () => {
        // Fallback to original if WebP fails
        if (finalSrc === webpSrc && webpSrc !== src) {
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            setCurrentSrc(src);
            setIsLoaded(true);
          };
          fallbackImg.src = src;
        } else {
          setIsLoaded(true);
        }
      };

      img.src = finalSrc;
    }
  }, [isInView, isLoaded, src, webpSrc]);

  return (
    <div
      id={`img-${src.replace(/[^a-zA-Z0-9]/g, '')}`}
      className={`relative overflow-hidden ${className}`}
      style={style}
    >
      <img
        src={currentSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-opacity duration-500 ${
          isLoaded ? 'opacity-100' : 'opacity-70'
        }`}
        style={{
          filter: isLoaded ? 'none' : 'blur(5px)',
          transform: isLoaded ? 'scale(1)' : 'scale(1.05)',
          transition: 'all 0.5s ease-out'
        }}
      />

      {!isLoaded && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse" />
      )}
    </div>
  );
};

export default OptimizedImage;
```

**ProgressiveBackgroundImage Component** (`components/ProgressiveBackgroundImage.tsx`):
```tsx
import React, { useState, useEffect } from 'react';

interface ProgressiveBackgroundImageProps {
  src: string;
  lowQualitySrc?: string;
  webpSrc?: string;
  className?: string;
  children: React.ReactNode;
  priority?: boolean;
}

const ProgressiveBackgroundImage: React.FC<ProgressiveBackgroundImageProps> = ({
  src,
  lowQualitySrc,
  webpSrc,
  className = '',
  children,
  priority = false
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(lowQualitySrc || '');
  const [isInView, setIsInView] = useState(priority);

  useEffect(() => {
    if (!priority) {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      const element = document.getElementById('progressive-bg');
      if (element) {
        observer.observe(element);
      }

      return () => observer.disconnect();
    }
  }, [priority]);

  useEffect(() => {
    if (isInView) {
      const img = new Image();

      // Check WebP support
      const supportsWebP = () => {
        const canvas = document.createElement('canvas');
        return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      };

      const finalSrc = (webpSrc && supportsWebP()) ? webpSrc : src;

      img.onload = () => {
        setCurrentSrc(finalSrc);
        setIsLoaded(true);
      };

      img.onerror = () => {
        // Fallback to original if WebP fails
        if (finalSrc === webpSrc && webpSrc !== src) {
          const fallbackImg = new Image();
          fallbackImg.onload = () => {
            setCurrentSrc(src);
            setIsLoaded(true);
          };
          fallbackImg.src = src;
        } else {
          setCurrentSrc(src);
          setIsLoaded(true);
        }
      };

      img.src = finalSrc;
    }
  }, [isInView, src, webpSrc]);

  return (
    <div
      id="progressive-bg"
      className={`relative ${className}`}
      style={{
        backgroundImage: currentSrc ? `url(${currentSrc})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        transition: 'all 0.5s ease-out',
        filter: isLoaded ? 'none' : 'blur(2px)',
        transform: isLoaded ? 'scale(1)' : 'scale(1.02)'
      }}
    >
      {/* Loading placeholder */}
      {!currentSrc && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-300 via-gray-200 to-gray-400 animate-pulse" />
      )}

      {/* Content */}
      {children}
    </div>
  );
};

export default ProgressiveBackgroundImage;
```

### 4. **HTML Optimizations** (`index.html`)
```html
<!-- Font optimization -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

<!-- Critical font preloading -->
<link rel="preload" href="[FONT_URL]" as="font" type="font/woff2" crossorigin>

<!-- Critical image preloading -->
<link rel="preload" href="/images/hero-low.jpg" as="image">
<link rel="preload" href="/images/hero.webp" as="image" type="image/webp">

<!-- Font loading optimization CSS -->
<style>
  .font-body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-display: swap;
  }
  
  .font-loading { visibility: hidden; }
  .fonts-loaded .font-loading { visibility: visible; }
  
  .progressive-image {
    transition: filter 0.3s ease, transform 0.3s ease;
  }
  .progressive-image.loading {
    filter: blur(5px);
    transform: scale(1.02);
  }
  .progressive-image.loaded {
    filter: none;
    transform: scale(1);
  }
</style>

<!-- Font loading detection -->
<script>
  if ('fonts' in document) {
    document.fonts.ready.then(() => {
      document.documentElement.classList.add('fonts-loaded');
    });
  } else {
    setTimeout(() => {
      document.documentElement.classList.add('fonts-loaded');
    }, 100);
  }
</script>
```

### 5. **Cache Headers** (`public/_headers`)
```
# Long cache for optimized assets
/assets/*
  Cache-Control: public, max-age=********, immutable

/images/*.webp
  Cache-Control: public, max-age=********, immutable

/images/*-optimized.*
  Cache-Control: public, max-age=********, immutable

# No cache for translation files
/locales/*.json
  Cache-Control: no-cache, no-store, must-revalidate

# Security headers
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
```

### 6. **Cloudflare Workers Integration** (if applicable)
- Static asset serving with proper cache headers
- SPA routing support
- Image optimization headers
- Translation file cache control

## 🎯 Usage Instructions

### **For New Projects:**
1. Copy all template files to project
2. Update `package.json` scripts
3. Install ImageMagick: `brew install imagemagick`
4. Replace image components with optimized versions
5. Run `npm run build` (automatic optimization)

### **Component Usage:**
```tsx
// Regular images
<OptimizedImage
  src="/images/photo-optimized.jpg"
  webpSrc="/images/photo.webp"
  lowQualitySrc="/images/photo-low.jpg"
  alt="Description"
  className="w-full h-64"
/>

// Background images (hero sections)
<ProgressiveBackgroundImage
  src="/images/hero-optimized.png"
  webpSrc="/images/hero.webp"
  lowQualitySrc="/images/hero-low.jpg"
  priority={true}
  className="hero-section"
>
  <div>Hero content</div>
</ProgressiveBackgroundImage>
```

### **Available Commands:**
```bash
npm run build          # Build with automatic optimization (local)
npm run build:ci       # Build without optimization (CI/CD)
npm run build:fast     # Build without optimization (dev)
npm run optimize-images # Optimize all images manually
npm run optimize-image filename.png # Optimize specific image
npm run deploy         # Build + optimize + deploy
```

### **Deployment Configuration:**

**Cloudflare Workers/Pages Settings:**
- **Build command**: `npm run build:ci`
- **Deploy command**: `npx wrangler deploy`
- **Build output directory**: `dist`

**Why `build:ci`?**
- CI environments don't have ImageMagick
- Uses pre-optimized images from local development
- Prevents build failures in deployment

## 📊 Expected Performance Results

### **Image Optimization:**
- **60-94% file size reduction** with WebP
- **Instant loading** with tiny placeholders
- **Smooth transitions** with progressive loading
- **Automatic fallbacks** for older browsers

### **Font Loading:**
- **No FOUT** (Flash of Unstyled Text)
- **Instant text display** with system fonts
- **Smooth transitions** to web fonts
- **Faster perceived loading**

### **Overall Performance:**
- **75-80% bandwidth reduction**
- **Professional loading experience**
- **Excellent Core Web Vitals scores**
- **Fast on all devices and connections**

## 🔧 Technical Features

### **Smart Optimization:**
- Only processes changed images
- Maintains file timestamps
- Skips already optimized files
- Automatic during build process

### **Progressive Enhancement:**
- Works without JavaScript
- Graceful fallbacks everywhere
- WebP detection with PNG/JPG fallback
- System fonts while web fonts load

### **Performance Monitoring:**
- Intersection Observer for lazy loading
- Will-change hints for animations
- Optimized cache headers
- Preloading for critical resources

## 🚀 Deployment Integration

### **Cloudflare Workers:**
- Automatic static asset serving
- Proper cache headers
- SPA routing support
- Image optimization headers
- See `CLOUDFLARE_WORKERS_DEPLOYMENT_GUIDE.md` for complete setup

### **Other Platforms:**
- Works with any static hosting
- CDN-friendly optimization
- Standard web performance practices
- Universal compatibility

## 💡 Maintenance

### **Adding New Images:**
1. Add image to `public/images/`
2. Run `npm run optimize-image filename.ext`
3. Use OptimizedImage component in code
4. Build automatically optimizes all images

### **Updating Fonts:**
1. Update font URLs in `index.html`
2. Add preload links for critical fonts
3. Update font-family fallbacks in CSS

### **Performance Monitoring:**
- Use browser DevTools to verify optimizations
- Check WebP support and fallbacks
- Monitor Core Web Vitals
- Test on slow connections

---

## 🎉 Result

After implementing this template, your website will:
- **Load 75-80% faster** with optimized images
- **Display text instantly** without font flash
- **Provide smooth, professional loading experience**
- **Work perfectly on all devices and connections**
- **Maintain excellent performance automatically**

Just provide this template file and specify your project's image locations, and all optimizations will be set up seamlessly!
